using System;
using System.Data.SQLite;
using System.IO;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AlNoorEducationalInstitute.Data
{
    /// <summary>
    /// مدير قاعدة البيانات - مسؤول عن إنشاء وإدارة قاعدة البيانات
    /// Database Manager - Responsible for creating and managing the database
    /// </summary>
    public class DatabaseManager
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DatabaseManager> _logger;
        private readonly string _connectionString;
        private readonly string _databasePath;

        public DatabaseManager(IConfiguration configuration, ILogger<DatabaseManager> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _connectionString = _configuration.GetConnectionString("DefaultConnection") 
                ?? throw new InvalidOperationException("Connection string not found");
            
            // استخراج مسار قاعدة البيانات من connection string
            var builder = new SQLiteConnectionStringBuilder(_connectionString);
            _databasePath = builder.DataSource;
        }

        /// <summary>
        /// تهيئة قاعدة البيانات - إنشاء قاعدة البيانات والجداول إذا لم تكن موجودة
        /// </summary>
        public void InitializeDatabase()
        {
            try
            {
                _logger.LogInformation("بدء تهيئة قاعدة البيانات...");

                // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
                var databaseDirectory = Path.GetDirectoryName(_databasePath);
                if (!string.IsNullOrEmpty(databaseDirectory) && !Directory.Exists(databaseDirectory))
                {
                    Directory.CreateDirectory(databaseDirectory);
                    _logger.LogInformation($"تم إنشاء مجلد قاعدة البيانات: {databaseDirectory}");
                }

                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                if (!File.Exists(_databasePath))
                {
                    SQLiteConnection.CreateFile(_databasePath);
                    _logger.LogInformation($"تم إنشاء ملف قاعدة البيانات: {_databasePath}");
                }

                // إنشاء الجداول
                CreateTables();

                // إدراج البيانات الأولية
                InsertInitialData();

                _logger.LogInformation("تم إكمال تهيئة قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تهيئة قاعدة البيانات");
                throw;
            }
        }

        /// <summary>
        /// إنشاء جميع الجداول المطلوبة
        /// </summary>
        private void CreateTables()
        {
            using var connection = new SQLiteConnection(_connectionString);
            connection.Open();

            // إنشاء جدول المستخدمين
            CreateUsersTable(connection);

            // إنشاء جدول الموظفين
            CreateEmployeesTable(connection);

            // إنشاء جدول الفصول الدراسية
            CreateClassesTable(connection);

            // إنشاء جدول المواد الدراسية
            CreateSubjectsTable(connection);

            // إنشاء جدول الطلاب
            CreateStudentsTable(connection);

            // إنشاء جدول ربط الفصول بالمواد
            CreateClassSubjectsTable(connection);

            // إنشاء جدول سجل العمليات
            CreateAuditLogTable(connection);

            // إنشاء جداول النظام المالي
            CreateFeesTable(connection);
            CreateInvoicesTable(connection);
            CreateInvoiceItemsTable(connection);
            CreatePaymentsTable(connection);

            // إنشاء جداول النظام الأكاديمي
            CreateGradesTable(connection);
            CreateAttendanceTable(connection);
            CreateStudentReportsTable(connection);
            CreateSubjectGradesTable(connection);

            _logger.LogInformation("تم إنشاء جميع الجداول بنجاح");
        }

        /// <summary>
        /// إنشاء جدول المستخدمين
        /// </summary>
        private void CreateUsersTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Users (
                    UserId INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT NOT NULL UNIQUE,
                    PasswordHash TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    Email TEXT NOT NULL UNIQUE,
                    Phone TEXT,
                    Role INTEGER NOT NULL,
                    Permissions TEXT,
                    Status INTEGER NOT NULL DEFAULT 1,
                    LastLoginDate DATETIME,
                    FailedLoginAttempts INTEGER NOT NULL DEFAULT 0,
                    AccountLockedUntil DATETIME,
                    PasswordExpiryDate DATETIME,
                    MustChangePassword BOOLEAN NOT NULL DEFAULT 0,
                    EmployeeId INTEGER,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (EmployeeId) REFERENCES Employees(EmployeeId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول الموظفين
        /// </summary>
        private void CreateEmployeesTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Employees (
                    EmployeeId INTEGER PRIMARY KEY AUTOINCREMENT,
                    EmployeeNumber TEXT NOT NULL UNIQUE,
                    FullNameArabic TEXT NOT NULL,
                    FullNameEnglish TEXT,
                    DateOfBirth DATE NOT NULL,
                    PlaceOfBirth TEXT,
                    Gender INTEGER NOT NULL,
                    Nationality TEXT,
                    NationalId TEXT NOT NULL UNIQUE,
                    Phone TEXT NOT NULL,
                    Phone2 TEXT,
                    Email TEXT NOT NULL UNIQUE,
                    Address TEXT NOT NULL,
                    Qualification TEXT NOT NULL,
                    Specialization TEXT,
                    YearsOfExperience INTEGER NOT NULL DEFAULT 0,
                    PreviousExperience TEXT,
                    Position INTEGER NOT NULL,
                    Department TEXT,
                    HireDate DATE NOT NULL,
                    Salary DECIMAL(10,2) NOT NULL DEFAULT 0,
                    Status INTEGER NOT NULL DEFAULT 1,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    UserId INTEGER,
                    FOREIGN KEY (UserId) REFERENCES Users(UserId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول الفصول الدراسية
        /// </summary>
        private void CreateClassesTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Classes (
                    ClassId INTEGER PRIMARY KEY AUTOINCREMENT,
                    ClassName TEXT NOT NULL,
                    Level INTEGER NOT NULL,
                    Grade INTEGER NOT NULL,
                    Section TEXT NOT NULL,
                    AcademicYear TEXT NOT NULL,
                    MaxStudents INTEGER NOT NULL DEFAULT 30,
                    CurrentStudentCount INTEGER NOT NULL DEFAULT 0,
                    ClassTeacherId INTEGER,
                    RoomNumber TEXT,
                    Status INTEGER NOT NULL DEFAULT 1,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (ClassTeacherId) REFERENCES Employees(EmployeeId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول المواد الدراسية
        /// </summary>
        private void CreateSubjectsTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Subjects (
                    SubjectId INTEGER PRIMARY KEY AUTOINCREMENT,
                    SubjectCode TEXT NOT NULL UNIQUE,
                    SubjectNameArabic TEXT NOT NULL,
                    SubjectNameEnglish TEXT,
                    Description TEXT,
                    Type INTEGER NOT NULL,
                    Level INTEGER NOT NULL,
                    Grade INTEGER NOT NULL,
                    WeeklyHours INTEGER NOT NULL DEFAULT 1,
                    MaxGrade INTEGER NOT NULL DEFAULT 100,
                    PassingGrade INTEGER NOT NULL DEFAULT 50,
                    TeacherId INTEGER,
                    TextBook TEXT,
                    AdditionalReferences TEXT,
                    Prerequisites TEXT,
                    Status INTEGER NOT NULL DEFAULT 1,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (TeacherId) REFERENCES Employees(EmployeeId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول الطلاب
        /// </summary>
        private void CreateStudentsTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Students (
                    StudentId INTEGER PRIMARY KEY AUTOINCREMENT,
                    StudentNumber TEXT NOT NULL UNIQUE,
                    FullNameArabic TEXT NOT NULL,
                    FullNameEnglish TEXT,
                    DateOfBirth DATE NOT NULL,
                    PlaceOfBirth TEXT,
                    Gender INTEGER NOT NULL,
                    Nationality TEXT,
                    GuardianName TEXT NOT NULL,
                    GuardianRelationship TEXT,
                    GuardianOccupation TEXT,
                    GuardianPhone TEXT NOT NULL,
                    GuardianPhone2 TEXT,
                    GuardianEmail TEXT,
                    Address TEXT NOT NULL,
                    CurrentClassId INTEGER,
                    EnrollmentDate DATE NOT NULL,
                    Status INTEGER NOT NULL DEFAULT 1,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (CurrentClassId) REFERENCES Classes(ClassId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول ربط الفصول بالمواد
        /// </summary>
        private void CreateClassSubjectsTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS ClassSubjects (
                    ClassSubjectId INTEGER PRIMARY KEY AUTOINCREMENT,
                    ClassId INTEGER NOT NULL,
                    SubjectId INTEGER NOT NULL,
                    TeacherId INTEGER,
                    AcademicYear TEXT NOT NULL,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (ClassId) REFERENCES Classes(ClassId),
                    FOREIGN KEY (SubjectId) REFERENCES Subjects(SubjectId),
                    FOREIGN KEY (TeacherId) REFERENCES Employees(EmployeeId),
                    UNIQUE(ClassId, SubjectId, AcademicYear)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول سجل العمليات
        /// </summary>
        private void CreateAuditLogTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS AuditLog (
                    LogId INTEGER PRIMARY KEY AUTOINCREMENT,
                    TableName TEXT NOT NULL,
                    RecordId INTEGER NOT NULL,
                    Operation TEXT NOT NULL,
                    OldValues TEXT,
                    NewValues TEXT,
                    UserId INTEGER NOT NULL,
                    UserName TEXT NOT NULL,
                    Timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    IPAddress TEXT,
                    UserAgent TEXT
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إدراج البيانات الأولية
        /// </summary>
        private void InsertInitialData()
        {
            using var connection = new SQLiteConnection(_connectionString);
            connection.Open();

            // التحقق من وجود مستخدم مدير النظام
            var checkAdminSql = "SELECT COUNT(*) FROM Users WHERE Role = 1";
            using var checkCommand = new SQLiteCommand(checkAdminSql, connection);
            var adminCount = Convert.ToInt32(checkCommand.ExecuteScalar());

            if (adminCount == 0)
            {
                // إنشاء مستخدم مدير النظام الافتراضي
                var adminSql = @"
                    INSERT INTO Users (Username, PasswordHash, FullName, Email, Role, Status, CreatedByUserId, LastModifiedByUserId)
                    VALUES ('admin', '$2a$11$N9qo8uLOickgx2ZMRZoMye.IjdJGjrdpnxuOKnfy.lMMjlaOCOUzK', 'مدير النظام', '<EMAIL>', 1, 1, 1, 1)";

                using var adminCommand = new SQLiteCommand(adminSql, connection);
                adminCommand.ExecuteNonQuery();

                _logger.LogInformation("تم إنشاء حساب مدير النظام الافتراضي");
                _logger.LogInformation("اسم المستخدم: admin");
                _logger.LogInformation("كلمة المرور: admin123");
            }
        }

        /// <summary>
        /// الحصول على اتصال جديد بقاعدة البيانات
        /// </summary>
        public SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(_connectionString);
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        public int ExecuteNonQuery(string sql, params SQLiteParameter[] parameters)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(sql, connection);

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return command.ExecuteNonQuery();
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع قيمة واحدة
        /// </summary>
        public object? ExecuteScalar(string sql, params SQLiteParameter[] parameters)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(sql, connection);

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return command.ExecuteScalar();
        }

        /// <summary>
        /// إنشاء جدول الرسوم الدراسية
        /// </summary>
        private void CreateFeesTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Fees (
                    FeeId INTEGER PRIMARY KEY AUTOINCREMENT,
                    FeeName TEXT NOT NULL,
                    Description TEXT,
                    Type INTEGER NOT NULL,
                    Amount DECIMAL(10,2) NOT NULL,
                    ApplicableLevel INTEGER,
                    ApplicableGrade INTEGER,
                    IsMandatory BOOLEAN NOT NULL DEFAULT 1,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    EffectiveDate DATE NOT NULL,
                    ExpiryDate DATE,
                    AcademicYear TEXT NOT NULL,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول الفواتير
        /// </summary>
        private void CreateInvoicesTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Invoices (
                    InvoiceId INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL UNIQUE,
                    StudentId INTEGER NOT NULL,
                    IssueDate DATE NOT NULL,
                    DueDate DATE NOT NULL,
                    TotalAmount DECIMAL(10,2) NOT NULL,
                    PaidAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
                    Status INTEGER NOT NULL,
                    Type INTEGER NOT NULL,
                    ApplicableMonth INTEGER,
                    ApplicableYear INTEGER NOT NULL,
                    AcademicYear TEXT NOT NULL,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (StudentId) REFERENCES Students(StudentId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول عناصر الفاتورة
        /// </summary>
        private void CreateInvoiceItemsTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS InvoiceItems (
                    InvoiceItemId INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceId INTEGER NOT NULL,
                    FeeId INTEGER NOT NULL,
                    ItemName TEXT NOT NULL,
                    Description TEXT,
                    Quantity INTEGER NOT NULL DEFAULT 1,
                    UnitPrice DECIMAL(10,2) NOT NULL,
                    DiscountPercentage DECIMAL(5,2) NOT NULL DEFAULT 0,
                    DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (InvoiceId) REFERENCES Invoices(InvoiceId),
                    FOREIGN KEY (FeeId) REFERENCES Fees(FeeId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول الدفعات
        /// </summary>
        private void CreatePaymentsTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Payments (
                    PaymentId INTEGER PRIMARY KEY AUTOINCREMENT,
                    PaymentNumber TEXT NOT NULL UNIQUE,
                    InvoiceId INTEGER NOT NULL,
                    StudentId INTEGER NOT NULL,
                    Amount DECIMAL(10,2) NOT NULL,
                    PaymentDate DATE NOT NULL,
                    Method INTEGER NOT NULL,
                    ReferenceNumber TEXT,
                    BankName TEXT,
                    TransactionDate DATE,
                    Status INTEGER NOT NULL,
                    PayerName TEXT NOT NULL,
                    PayerPhone TEXT,
                    Notes TEXT,
                    ReceiptNumber TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (InvoiceId) REFERENCES Invoices(InvoiceId),
                    FOREIGN KEY (StudentId) REFERENCES Students(StudentId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول الدرجات
        /// </summary>
        private void CreateGradesTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Grades (
                    GradeId INTEGER PRIMARY KEY AUTOINCREMENT,
                    StudentId INTEGER NOT NULL,
                    SubjectId INTEGER NOT NULL,
                    ClassId INTEGER NOT NULL,
                    AssessmentType INTEGER NOT NULL,
                    AssessmentName TEXT NOT NULL,
                    Score DECIMAL(5,2) NOT NULL,
                    MaxScore DECIMAL(5,2) NOT NULL,
                    Weight DECIMAL(5,2) NOT NULL DEFAULT 100,
                    Semester INTEGER NOT NULL,
                    AcademicYear TEXT NOT NULL,
                    AssessmentDate DATE NOT NULL,
                    IsApproved BOOLEAN NOT NULL DEFAULT 0,
                    ApprovalDate DATETIME,
                    ApprovedByUserId INTEGER,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (StudentId) REFERENCES Students(StudentId),
                    FOREIGN KEY (SubjectId) REFERENCES Subjects(SubjectId),
                    FOREIGN KEY (ClassId) REFERENCES Classes(ClassId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول الحضور والغياب
        /// </summary>
        private void CreateAttendanceTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS Attendance (
                    AttendanceId INTEGER PRIMARY KEY AUTOINCREMENT,
                    StudentId INTEGER NOT NULL,
                    ClassId INTEGER NOT NULL,
                    SubjectId INTEGER,
                    AttendanceDate DATE NOT NULL,
                    Type INTEGER NOT NULL,
                    Status INTEGER NOT NULL,
                    ArrivalTime TIME,
                    DepartureTime TIME,
                    PeriodNumber INTEGER,
                    AbsenceReason TEXT,
                    IsExcused BOOLEAN NOT NULL DEFAULT 0,
                    ExcuseDocument TEXT,
                    Semester INTEGER NOT NULL,
                    AcademicYear TEXT NOT NULL,
                    Notes TEXT,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (StudentId) REFERENCES Students(StudentId),
                    FOREIGN KEY (ClassId) REFERENCES Classes(ClassId),
                    FOREIGN KEY (SubjectId) REFERENCES Subjects(SubjectId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول تقارير الطلاب
        /// </summary>
        private void CreateStudentReportsTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS StudentReports (
                    ReportId INTEGER PRIMARY KEY AUTOINCREMENT,
                    StudentId INTEGER NOT NULL,
                    ClassId INTEGER NOT NULL,
                    Semester INTEGER NOT NULL,
                    AcademicYear TEXT NOT NULL,
                    OverallAverage DECIMAL(5,2) NOT NULL,
                    TotalScore DECIMAL(8,2) NOT NULL,
                    TotalMaxScore DECIMAL(8,2) NOT NULL,
                    PassedSubjects INTEGER NOT NULL,
                    FailedSubjects INTEGER NOT NULL,
                    TotalSubjects INTEGER NOT NULL,
                    ClassRank INTEGER,
                    TotalClassStudents INTEGER,
                    AttendancePercentage DECIMAL(5,2) NOT NULL,
                    AttendanceDays INTEGER NOT NULL,
                    AbsenceDays INTEGER NOT NULL,
                    ExcusedAbsenceDays INTEGER NOT NULL,
                    OverallGrade TEXT,
                    Status INTEGER NOT NULL,
                    TeacherComments TEXT,
                    AdministrationComments TEXT,
                    IssueDate DATE NOT NULL,
                    IsApproved BOOLEAN NOT NULL DEFAULT 0,
                    ApprovalDate DATETIME,
                    ApprovedByUserId INTEGER,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModifiedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedByUserId INTEGER NOT NULL,
                    LastModifiedByUserId INTEGER NOT NULL,
                    FOREIGN KEY (StudentId) REFERENCES Students(StudentId),
                    FOREIGN KEY (ClassId) REFERENCES Classes(ClassId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء جدول درجات المواد في التقارير
        /// </summary>
        private void CreateSubjectGradesTable(SQLiteConnection connection)
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS SubjectGrades (
                    SubjectGradeId INTEGER PRIMARY KEY AUTOINCREMENT,
                    ReportId INTEGER NOT NULL,
                    SubjectId INTEGER NOT NULL,
                    SubjectName TEXT NOT NULL,
                    Score DECIMAL(5,2) NOT NULL,
                    MaxScore DECIMAL(5,2) NOT NULL,
                    Percentage DECIMAL(5,2) NOT NULL,
                    Grade TEXT NOT NULL,
                    IsPassed BOOLEAN NOT NULL,
                    TeacherName TEXT,
                    FOREIGN KEY (ReportId) REFERENCES StudentReports(ReportId),
                    FOREIGN KEY (SubjectId) REFERENCES Subjects(SubjectId)
                );";

            using var command = new SQLiteCommand(sql, connection);
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        public void CreateBackup(string backupPath)
        {
            try
            {
                var backupDirectory = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDirectory) && !Directory.Exists(backupDirectory))
                {
                    Directory.CreateDirectory(backupDirectory);
                }

                File.Copy(_databasePath, backupPath, true);
                _logger.LogInformation($"تم إنشاء نسخة احتياطية في: {backupPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية");
                throw;
            }
        }
    }
}
