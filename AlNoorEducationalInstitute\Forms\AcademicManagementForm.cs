using System;
using System.Drawing;
using System.Windows.Forms;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة إدارة النظام الأكاديمي
    /// Academic management form
    /// </summary>
    public partial class AcademicManagementForm : Form
    {
        private readonly IAcademicService _academicService;
        private readonly IStudentService _studentService;
        private readonly IClassService _classService;
        private readonly ISubjectService _subjectService;
        
        private TabControl tabControl;
        private TabPage tabGrades;
        private TabPage tabAttendance;
        private TabPage tabReports;
        private TabPage tabStatistics;

        // Controls for Grades tab
        private DataGridView dgvGrades;
        private Panel pnlGradesTop;
        private ComboBox cmbGradeClass;
        private ComboBox cmbGradeSubject;
        private ComboBox cmbGradeSemester;
        private Button btnAddGrade;
        private Button btnEditGrade;
        private Button btnApproveGrades;

        // Controls for Attendance tab
        private DataGridView dgvAttendance;
        private Panel pnlAttendanceTop;
        private ComboBox cmbAttendanceClass;
        private DateTimePicker dtpAttendanceDate;
        private Button btnMarkAttendance;
        private Button btnViewAttendance;

        // Controls for Reports tab
        private DataGridView dgvReports;
        private Panel pnlReportsTop;
        private ComboBox cmbReportClass;
        private ComboBox cmbReportSemester;
        private Button btnGenerateReport;
        private Button btnViewReport;
        private Button btnPrintReport;

        public AcademicManagementForm(
            IAcademicService academicService,
            IStudentService studentService,
            IClassService classService,
            ISubjectService subjectService)
        {
            _academicService = academicService;
            _studentService = studentService;
            _classService = classService;
            _subjectService = subjectService;
            
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "إدارة النظام الأكاديمي";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 10F, FontStyle.Regular)
            };

            // Grades tab
            tabGrades = new TabPage("إدارة الدرجات");
            SetupGradesTab();

            // Attendance tab
            tabAttendance = new TabPage("الحضور والغياب");
            SetupAttendanceTab();

            // Reports tab
            tabReports = new TabPage("كشوف النقاط");
            SetupReportsTab();

            // Statistics tab
            tabStatistics = new TabPage("الإحصائيات");
            SetupStatisticsTab();

            // Add tabs to control
            tabControl.TabPages.AddRange(new TabPage[] { tabGrades, tabAttendance, tabReports, tabStatistics });

            // Add tab control to form
            this.Controls.Add(tabControl);

            this.ResumeLayout(false);
        }

        private void SetupGradesTab()
        {
            // Top panel for grades
            pnlGradesTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Class filter
            var lblGradeClass = new Label
            {
                Text = "الفصل:",
                Size = new Size(50, 25),
                Location = new Point(1100, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbGradeClass = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(940, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Subject filter
            var lblGradeSubject = new Label
            {
                Text = "المادة:",
                Size = new Size(50, 25),
                Location = new Point(880, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbGradeSubject = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(720, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Semester filter
            var lblGradeSemester = new Label
            {
                Text = "الفصل الدراسي:",
                Size = new Size(80, 25),
                Location = new Point(630, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbGradeSemester = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(500, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Buttons
            btnAddGrade = new Button
            {
                Text = "إضافة درجة",
                Size = new Size(100, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnEditGrade = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 35),
                Location = new Point(120, 12),
                BackColor = Color.FromArgb(255, 165, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnApproveGrades = new Button
            {
                Text = "اعتماد الدرجات",
                Size = new Size(120, 35),
                Location = new Point(210, 12),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlGradesTop.Controls.AddRange(new Control[]
            {
                lblGradeClass, cmbGradeClass, lblGradeSubject, cmbGradeSubject,
                lblGradeSemester, cmbGradeSemester, btnAddGrade, btnEditGrade, btnApproveGrades
            });

            // DataGridView for grades
            dgvGrades = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false
            };

            SetupGradesDataGridView();

            tabGrades.Controls.Add(dgvGrades);
            tabGrades.Controls.Add(pnlGradesTop);
        }

        private void SetupAttendanceTab()
        {
            // Top panel for attendance
            pnlAttendanceTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Class filter
            var lblAttendanceClass = new Label
            {
                Text = "الفصل:",
                Size = new Size(50, 25),
                Location = new Point(1100, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbAttendanceClass = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(940, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Date picker
            var lblAttendanceDate = new Label
            {
                Text = "التاريخ:",
                Size = new Size(50, 25),
                Location = new Point(880, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            dtpAttendanceDate = new DateTimePicker
            {
                Size = new Size(150, 25),
                Location = new Point(720, 15),
                Format = DateTimePickerFormat.Short
            };

            // Buttons
            btnMarkAttendance = new Button
            {
                Text = "تسجيل الحضور",
                Size = new Size(120, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnViewAttendance = new Button
            {
                Text = "عرض الحضور",
                Size = new Size(100, 35),
                Location = new Point(140, 12),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlAttendanceTop.Controls.AddRange(new Control[]
            {
                lblAttendanceClass, cmbAttendanceClass, lblAttendanceDate, dtpAttendanceDate,
                btnMarkAttendance, btnViewAttendance
            });

            // DataGridView for attendance
            dgvAttendance = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false
            };

            SetupAttendanceDataGridView();

            tabAttendance.Controls.Add(dgvAttendance);
            tabAttendance.Controls.Add(pnlAttendanceTop);
        }

        private void SetupReportsTab()
        {
            // Top panel for reports
            pnlReportsTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Class filter
            var lblReportClass = new Label
            {
                Text = "الفصل:",
                Size = new Size(50, 25),
                Location = new Point(1100, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbReportClass = new ComboBox
            {
                Size = new Size(150, 25),
                Location = new Point(940, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Semester filter
            var lblReportSemester = new Label
            {
                Text = "الفصل الدراسي:",
                Size = new Size(80, 25),
                Location = new Point(850, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbReportSemester = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(720, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Buttons
            btnGenerateReport = new Button
            {
                Text = "إنشاء كشف نقاط",
                Size = new Size(120, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnViewReport = new Button
            {
                Text = "عرض",
                Size = new Size(80, 35),
                Location = new Point(140, 12),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnPrintReport = new Button
            {
                Text = "طباعة",
                Size = new Size(80, 35),
                Location = new Point(230, 12),
                BackColor = Color.FromArgb(128, 0, 128),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlReportsTop.Controls.AddRange(new Control[]
            {
                lblReportClass, cmbReportClass, lblReportSemester, cmbReportSemester,
                btnGenerateReport, btnViewReport, btnPrintReport
            });

            // DataGridView for reports
            dgvReports = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false
            };

            SetupReportsDataGridView();

            tabReports.Controls.Add(dgvReports);
            tabReports.Controls.Add(pnlReportsTop);
        }

        private void SetupStatisticsTab()
        {
            var lblStatistics = new Label
            {
                Text = "الإحصائيات الأكاديمية - سيتم تطويرها لاحقاً",
                Font = new Font("Tahoma", 14F, FontStyle.Regular),
                ForeColor = Color.FromArgb(25, 25, 112),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            tabStatistics.Controls.Add(lblStatistics);
        }

        private void SetupGradesDataGridView()
        {
            dgvGrades.Columns.Clear();
            dgvGrades.Columns.Add("GradeId", "المعرف");
            dgvGrades.Columns.Add("StudentName", "اسم الطالب");
            dgvGrades.Columns.Add("SubjectName", "المادة");
            dgvGrades.Columns.Add("AssessmentName", "نوع التقييم");
            dgvGrades.Columns.Add("Score", "الدرجة");
            dgvGrades.Columns.Add("MaxScore", "الدرجة العظمى");
            dgvGrades.Columns.Add("Percentage", "النسبة المئوية");
            dgvGrades.Columns.Add("IsApproved", "معتمدة");
            dgvGrades.Columns["GradeId"].Visible = false;
        }

        private void SetupAttendanceDataGridView()
        {
            dgvAttendance.Columns.Clear();
            dgvAttendance.Columns.Add("AttendanceId", "المعرف");
            dgvAttendance.Columns.Add("StudentName", "اسم الطالب");
            dgvAttendance.Columns.Add("AttendanceDate", "التاريخ");
            dgvAttendance.Columns.Add("Status", "الحالة");
            dgvAttendance.Columns.Add("ArrivalTime", "وقت الوصول");
            dgvAttendance.Columns.Add("Notes", "ملاحظات");
            dgvAttendance.Columns["AttendanceId"].Visible = false;
        }

        private void SetupReportsDataGridView()
        {
            dgvReports.Columns.Clear();
            dgvReports.Columns.Add("ReportId", "المعرف");
            dgvReports.Columns.Add("StudentName", "اسم الطالب");
            dgvReports.Columns.Add("StudentNumber", "رقم الطالب");
            dgvReports.Columns.Add("OverallAverage", "المعدل العام");
            dgvReports.Columns.Add("ClassRank", "الترتيب");
            dgvReports.Columns.Add("AttendancePercentage", "نسبة الحضور");
            dgvReports.Columns.Add("Status", "الحالة");
            dgvReports.Columns["ReportId"].Visible = false;
        }

        private void SetupForm()
        {
            // Setup combo boxes
            SetupComboBoxes();

            // Event handlers
            btnAddGrade.Click += BtnAddGrade_Click;
            btnEditGrade.Click += BtnEditGrade_Click;
            btnApproveGrades.Click += BtnApproveGrades_Click;

            btnMarkAttendance.Click += BtnMarkAttendance_Click;
            btnViewAttendance.Click += BtnViewAttendance_Click;

            btnGenerateReport.Click += BtnGenerateReport_Click;
            btnViewReport.Click += BtnViewReport_Click;
            btnPrintReport.Click += BtnPrintReport_Click;

            // Button styling
            foreach (Control control in this.Controls)
            {
                if (control is TabControl tabCtrl)
                {
                    foreach (TabPage tab in tabCtrl.TabPages)
                    {
                        foreach (Control ctrl in tab.Controls)
                        {
                            if (ctrl is Panel panel)
                            {
                                foreach (Control btnCtrl in panel.Controls)
                                {
                                    if (btnCtrl is Button btn)
                                    {
                                        btn.FlatAppearance.BorderSize = 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        private void SetupComboBoxes()
        {
            // Semester combos
            var semesters = new[]
            {
                new ComboBoxItem("الفصل الأول", Semester.First),
                new ComboBoxItem("الفصل الثاني", Semester.Second),
                new ComboBoxItem("الفصل الثالث", Semester.Third)
            };

            cmbGradeSemester.Items.AddRange(semesters);
            cmbReportSemester.Items.AddRange(semesters);
            
            if (cmbGradeSemester.Items.Count > 0) cmbGradeSemester.SelectedIndex = 0;
            if (cmbReportSemester.Items.Count > 0) cmbReportSemester.SelectedIndex = 0;

            // Set default date
            dtpAttendanceDate.Value = DateTime.Today;
        }

        private async void LoadData()
        {
            try
            {
                // Load classes for combo boxes
                var classes = await _classService.GetAllClassesAsync();
                foreach (var cls in classes)
                {
                    var item = new ComboBoxItem(cls.ClassName, cls.ClassId);
                    cmbGradeClass.Items.Add(item);
                    cmbAttendanceClass.Items.Add(item);
                    cmbReportClass.Items.Add(item);
                }

                // Load subjects for combo box
                var subjects = await _subjectService.GetAllSubjectsAsync();
                foreach (var subject in subjects)
                {
                    cmbGradeSubject.Items.Add(new ComboBoxItem(subject.SubjectNameArabic, subject.SubjectId));
                }

                // Set default selections
                if (cmbGradeClass.Items.Count > 0) cmbGradeClass.SelectedIndex = 0;
                if (cmbAttendanceClass.Items.Count > 0) cmbAttendanceClass.SelectedIndex = 0;
                if (cmbReportClass.Items.Count > 0) cmbReportClass.SelectedIndex = 0;
                if (cmbGradeSubject.Items.Count > 0) cmbGradeSubject.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Event handlers
        private void BtnAddGrade_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة درجة جديدة", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnEditGrade_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة تعديل الدرجة", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnApproveGrades_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم اعتماد الدرجات المحددة", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnMarkAttendance_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة تسجيل الحضور", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnViewAttendance_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم عرض سجل الحضور", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnGenerateReport_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم إنشاء كشوف النقاط", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnViewReport_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم عرض كشف النقاط", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnPrintReport_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم طباعة كشف النقاط", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
