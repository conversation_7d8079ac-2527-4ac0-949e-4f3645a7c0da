using System;
using System.Drawing;
using System.Windows.Forms;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة إدارة الفصول الدراسية
    /// Class management form
    /// </summary>
    public partial class ClassManagementForm : Form
    {
        private readonly IClassService _classService;
        private DataGridView dgvClasses;
        private Panel pnlTop;
        private Panel pnlBottom;
        private ComboBox cmbLevel;
        private ComboBox cmbGrade;
        private ComboBox cmbAcademicYear;
        private Button btnFilter;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnRefresh;
        private Label lblLevel;
        private Label lblGrade;
        private Label lblAcademicYear;

        public ClassManagementForm(IClassService classService)
        {
            _classService = classService;
            InitializeComponent();
            SetupForm();
            LoadClasses();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "إدارة الفصول الدراسية";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Top panel
            pnlTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Filter controls
            lblLevel = new Label
            {
                Text = "المستوى:",
                Size = new Size(60, 25),
                Location = new Point(920, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbLevel = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(790, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            lblGrade = new Label
            {
                Text = "الصف:",
                Size = new Size(50, 25),
                Location = new Point(730, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbGrade = new ComboBox
            {
                Size = new Size(80, 25),
                Location = new Point(640, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            lblAcademicYear = new Label
            {
                Text = "العام الدراسي:",
                Size = new Size(80, 25),
                Location = new Point(550, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbAcademicYear = new ComboBox
            {
                Size = new Size(100, 25),
                Location = new Point(440, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            btnFilter = new Button
            {
                Text = "تصفية",
                Size = new Size(60, 25),
                Location = new Point(370, 15),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlTop.Controls.AddRange(new Control[]
            {
                lblLevel, cmbLevel, lblGrade, cmbGrade, lblAcademicYear, cmbAcademicYear, btnFilter
            });

            // Bottom panel
            pnlBottom = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Buttons
            btnAdd = new Button
            {
                Text = "إضافة فصل",
                Size = new Size(100, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnEdit = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 35),
                Location = new Point(120, 12),
                BackColor = Color.FromArgb(255, 165, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnDelete = new Button
            {
                Text = "حذف",
                Size = new Size(80, 35),
                Location = new Point(210, 12),
                BackColor = Color.FromArgb(220, 20, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnRefresh = new Button
            {
                Text = "تحديث",
                Size = new Size(80, 35),
                Location = new Point(300, 12),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlBottom.Controls.AddRange(new Control[]
            {
                btnAdd, btnEdit, btnDelete, btnRefresh
            });

            // DataGridView
            dgvClasses = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                RowHeadersVisible = false
            };

            // Add controls to form
            this.Controls.Add(dgvClasses);
            this.Controls.Add(pnlBottom);
            this.Controls.Add(pnlTop);

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // Setup level combobox
            cmbLevel.Items.Add(new ComboBoxItem("الكل", null));
            cmbLevel.Items.Add(new ComboBoxItem("رياض الأطفال", EducationLevel.Kindergarten));
            cmbLevel.Items.Add(new ComboBoxItem("ابتدائي", EducationLevel.Elementary));
            cmbLevel.Items.Add(new ComboBoxItem("إعدادي", EducationLevel.Middle));
            cmbLevel.Items.Add(new ComboBoxItem("ثانوي", EducationLevel.High));
            cmbLevel.SelectedIndex = 0;

            // Setup grade combobox
            cmbGrade.Items.Add(new ComboBoxItem("الكل", null));
            for (int i = 1; i <= 12; i++)
            {
                cmbGrade.Items.Add(new ComboBoxItem($"الصف {i}", i));
            }
            cmbGrade.SelectedIndex = 0;

            // Setup academic year combobox
            cmbAcademicYear.Items.Add(new ComboBoxItem("الكل", null));
            var currentYear = DateTime.Now.Year;
            for (int i = currentYear - 2; i <= currentYear + 2; i++)
            {
                cmbAcademicYear.Items.Add(new ComboBoxItem($"{i}/{i + 1}", $"{i}/{i + 1}"));
            }
            cmbAcademicYear.SelectedIndex = 3; // Current year

            // Setup DataGridView columns
            SetupDataGridViewColumns();

            // Event handlers
            btnFilter.Click += BtnFilter_Click;
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;
            cmbLevel.SelectedIndexChanged += CmbLevel_SelectedIndexChanged;
            dgvClasses.CellDoubleClick += DgvClasses_CellDoubleClick;

            // Button styling
            foreach (Button btn in new[] { btnFilter, btnAdd, btnEdit, btnDelete, btnRefresh })
            {
                btn.FlatAppearance.BorderSize = 0;
            }
        }

        private void SetupDataGridViewColumns()
        {
            dgvClasses.Columns.Clear();

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ClassId",
                HeaderText = "المعرف",
                Visible = false
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ClassName",
                HeaderText = "اسم الفصل",
                Width = 150
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Level",
                HeaderText = "المستوى",
                Width = 100
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Grade",
                HeaderText = "الصف",
                Width = 80
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Section",
                HeaderText = "الشعبة",
                Width = 80
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AcademicYear",
                HeaderText = "العام الدراسي",
                Width = 100
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ClassTeacher",
                HeaderText = "المدرس المسؤول",
                Width = 150
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentStudentCount",
                HeaderText = "عدد الطلاب",
                Width = 80
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "MaxStudents",
                HeaderText = "الحد الأقصى",
                Width = 80
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RoomNumber",
                HeaderText = "رقم القاعة",
                Width = 80
            });

            dgvClasses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 80
            });
        }

        private async void LoadClasses()
        {
            try
            {
                dgvClasses.Rows.Clear();
                
                var classes = await _classService.GetAllClassesAsync();
                
                foreach (var classEntity in classes)
                {
                    var row = new DataGridViewRow();
                    row.CreateCells(dgvClasses);
                    
                    row.Cells["ClassId"].Value = classEntity.ClassId;
                    row.Cells["ClassName"].Value = classEntity.ClassName;
                    row.Cells["Level"].Value = GetLevelDisplayName(classEntity.Level);
                    row.Cells["Grade"].Value = classEntity.Grade;
                    row.Cells["Section"].Value = classEntity.Section;
                    row.Cells["AcademicYear"].Value = classEntity.AcademicYear;
                    row.Cells["ClassTeacher"].Value = classEntity.ClassTeacher?.FullNameArabic ?? "غير محدد";
                    row.Cells["CurrentStudentCount"].Value = classEntity.CurrentStudentCount;
                    row.Cells["MaxStudents"].Value = classEntity.MaxStudents;
                    row.Cells["RoomNumber"].Value = classEntity.RoomNumber ?? "غير محدد";
                    row.Cells["Status"].Value = GetStatusDisplayName(classEntity.Status);
                    
                    dgvClasses.Rows.Add(row);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الفصول: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetLevelDisplayName(EducationLevel level)
        {
            return level switch
            {
                EducationLevel.Kindergarten => "رياض الأطفال",
                EducationLevel.Elementary => "ابتدائي",
                EducationLevel.Middle => "إعدادي",
                EducationLevel.High => "ثانوي",
                _ => "غير محدد"
            };
        }

        private string GetStatusDisplayName(ClassStatus status)
        {
            return status switch
            {
                ClassStatus.Active => "نشط",
                ClassStatus.Closed => "مغلق",
                ClassStatus.Suspended => "معلق",
                _ => "غير محدد"
            };
        }

        // Event handlers
        private void CmbLevel_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Update grade combobox based on selected level
            var selectedLevel = ((ComboBoxItem)cmbLevel.SelectedItem)?.Value as EducationLevel?;
            
            cmbGrade.Items.Clear();
            cmbGrade.Items.Add(new ComboBoxItem("الكل", null));
            
            if (selectedLevel.HasValue)
            {
                var maxGrade = selectedLevel.Value switch
                {
                    EducationLevel.Kindergarten => 2,
                    EducationLevel.Elementary => 6,
                    EducationLevel.Middle => 3,
                    EducationLevel.High => 3,
                    _ => 12
                };

                for (int i = 1; i <= maxGrade; i++)
                {
                    cmbGrade.Items.Add(new ComboBoxItem($"الصف {i}", i));
                }
            }
            else
            {
                for (int i = 1; i <= 12; i++)
                {
                    cmbGrade.Items.Add(new ComboBoxItem($"الصف {i}", i));
                }
            }
            
            cmbGrade.SelectedIndex = 0;
        }

        private void BtnFilter_Click(object sender, EventArgs e) => PerformFilter();
        private void BtnRefresh_Click(object sender, EventArgs e) => LoadClasses();

        private void PerformFilter()
        {
            MessageBox.Show("سيتم تطبيق وظيفة التصفية لاحقاً", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة فصل جديد", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvClasses.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار فصل للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("سيتم فتح نافذة تعديل بيانات الفصل", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvClasses.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار فصل للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل تريد حذف الفصل المحدد؟", "تأكيد الحذف", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("سيتم تطبيق وظيفة الحذف لاحقاً", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DgvClasses_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }
    }
}
