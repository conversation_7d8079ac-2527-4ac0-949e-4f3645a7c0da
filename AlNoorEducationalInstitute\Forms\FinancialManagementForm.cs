using System;
using System.Drawing;
using System.Windows.Forms;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة إدارة النظام المالي
    /// Financial management form
    /// </summary>
    public partial class FinancialManagementForm : Form
    {
        private readonly IFinancialService _financialService;
        private readonly IStudentService _studentService;
        private TabControl tabControl;
        private TabPage tabFees;
        private TabPage tabInvoices;
        private TabPage tabPayments;
        private TabPage tabReports;

        // Controls for Fees tab
        private DataGridView dgvFees;
        private Panel pnlFeesTop;
        private Button btnAddFee;
        private Button btnEditFee;
        private Button btnDeleteFee;
        private ComboBox cmbFeeType;
        private ComboBox cmbFeeLevel;

        // Controls for Invoices tab
        private DataGridView dgvInvoices;
        private Panel pnlInvoicesTop;
        private Button btnGenerateInvoice;
        private Button btnViewInvoice;
        private Button btnPayInvoice;
        private ComboBox cmbInvoiceStatus;
        private DateTimePicker dtpInvoiceFrom;
        private DateTimePicker dtpInvoiceTo;

        // Controls for Payments tab
        private DataGridView dgvPayments;
        private Panel pnlPaymentsTop;
        private Button btnAddPayment;
        private Button btnViewPayment;
        private Button btnPrintReceipt;
        private ComboBox cmbPaymentMethod;
        private DateTimePicker dtpPaymentFrom;
        private DateTimePicker dtpPaymentTo;

        public FinancialManagementForm(IFinancialService financialService, IStudentService studentService)
        {
            _financialService = financialService;
            _studentService = studentService;
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "إدارة النظام المالي";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 10F, FontStyle.Regular)
            };

            // Fees tab
            tabFees = new TabPage("إدارة الرسوم");
            SetupFeesTab();

            // Invoices tab
            tabInvoices = new TabPage("إدارة الفواتير");
            SetupInvoicesTab();

            // Payments tab
            tabPayments = new TabPage("إدارة الدفعات");
            SetupPaymentsTab();

            // Reports tab
            tabReports = new TabPage("التقارير المالية");
            SetupReportsTab();

            // Add tabs to control
            tabControl.TabPages.AddRange(new TabPage[] { tabFees, tabInvoices, tabPayments, tabReports });

            // Add tab control to form
            this.Controls.Add(tabControl);

            this.ResumeLayout(false);
        }

        private void SetupFeesTab()
        {
            // Top panel for fees
            pnlFeesTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Fee type filter
            var lblFeeType = new Label
            {
                Text = "نوع الرسم:",
                Size = new Size(70, 25),
                Location = new Point(1100, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbFeeType = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(970, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Fee level filter
            var lblFeeLevel = new Label
            {
                Text = "المستوى:",
                Size = new Size(60, 25),
                Location = new Point(900, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbFeeLevel = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(770, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Buttons
            btnAddFee = new Button
            {
                Text = "إضافة رسم",
                Size = new Size(100, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnEditFee = new Button
            {
                Text = "تعديل",
                Size = new Size(80, 35),
                Location = new Point(120, 12),
                BackColor = Color.FromArgb(255, 165, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnDeleteFee = new Button
            {
                Text = "حذف",
                Size = new Size(80, 35),
                Location = new Point(210, 12),
                BackColor = Color.FromArgb(220, 20, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlFeesTop.Controls.AddRange(new Control[]
            {
                lblFeeType, cmbFeeType, lblFeeLevel, cmbFeeLevel,
                btnAddFee, btnEditFee, btnDeleteFee
            });

            // DataGridView for fees
            dgvFees = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false
            };

            SetupFeesDataGridView();

            tabFees.Controls.Add(dgvFees);
            tabFees.Controls.Add(pnlFeesTop);
        }

        private void SetupInvoicesTab()
        {
            // Top panel for invoices
            pnlInvoicesTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Invoice status filter
            var lblInvoiceStatus = new Label
            {
                Text = "حالة الفاتورة:",
                Size = new Size(80, 25),
                Location = new Point(1100, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbInvoiceStatus = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(970, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Date filters
            var lblDateFrom = new Label
            {
                Text = "من تاريخ:",
                Size = new Size(60, 25),
                Location = new Point(900, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            dtpInvoiceFrom = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(770, 15),
                Format = DateTimePickerFormat.Short
            };

            var lblDateTo = new Label
            {
                Text = "إلى تاريخ:",
                Size = new Size(60, 25),
                Location = new Point(700, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            dtpInvoiceTo = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(570, 15),
                Format = DateTimePickerFormat.Short
            };

            // Buttons
            btnGenerateInvoice = new Button
            {
                Text = "إنشاء فاتورة",
                Size = new Size(100, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnViewInvoice = new Button
            {
                Text = "عرض",
                Size = new Size(80, 35),
                Location = new Point(120, 12),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnPayInvoice = new Button
            {
                Text = "دفع",
                Size = new Size(80, 35),
                Location = new Point(210, 12),
                BackColor = Color.FromArgb(255, 165, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlInvoicesTop.Controls.AddRange(new Control[]
            {
                lblInvoiceStatus, cmbInvoiceStatus, lblDateFrom, dtpInvoiceFrom,
                lblDateTo, dtpInvoiceTo, btnGenerateInvoice, btnViewInvoice, btnPayInvoice
            });

            // DataGridView for invoices
            dgvInvoices = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false
            };

            SetupInvoicesDataGridView();

            tabInvoices.Controls.Add(dgvInvoices);
            tabInvoices.Controls.Add(pnlInvoicesTop);
        }

        private void SetupPaymentsTab()
        {
            // Similar setup for payments tab
            pnlPaymentsTop = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Payment method filter
            var lblPaymentMethod = new Label
            {
                Text = "طريقة الدفع:",
                Size = new Size(80, 25),
                Location = new Point(1100, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbPaymentMethod = new ComboBox
            {
                Size = new Size(120, 25),
                Location = new Point(970, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Date filters
            var lblPaymentDateFrom = new Label
            {
                Text = "من تاريخ:",
                Size = new Size(60, 25),
                Location = new Point(900, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            dtpPaymentFrom = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(770, 15),
                Format = DateTimePickerFormat.Short
            };

            var lblPaymentDateTo = new Label
            {
                Text = "إلى تاريخ:",
                Size = new Size(60, 25),
                Location = new Point(700, 15),
                TextAlign = ContentAlignment.MiddleRight
            };

            dtpPaymentTo = new DateTimePicker
            {
                Size = new Size(120, 25),
                Location = new Point(570, 15),
                Format = DateTimePickerFormat.Short
            };

            // Buttons
            btnAddPayment = new Button
            {
                Text = "تسجيل دفعة",
                Size = new Size(100, 35),
                Location = new Point(10, 12),
                BackColor = Color.FromArgb(34, 139, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnViewPayment = new Button
            {
                Text = "عرض",
                Size = new Size(80, 35),
                Location = new Point(120, 12),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnPrintReceipt = new Button
            {
                Text = "طباعة إيصال",
                Size = new Size(100, 35),
                Location = new Point(210, 12),
                BackColor = Color.FromArgb(128, 0, 128),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            pnlPaymentsTop.Controls.AddRange(new Control[]
            {
                lblPaymentMethod, cmbPaymentMethod, lblPaymentDateFrom, dtpPaymentFrom,
                lblPaymentDateTo, dtpPaymentTo, btnAddPayment, btnViewPayment, btnPrintReceipt
            });

            // DataGridView for payments
            dgvPayments = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false
            };

            SetupPaymentsDataGridView();

            tabPayments.Controls.Add(dgvPayments);
            tabPayments.Controls.Add(pnlPaymentsTop);
        }

        private void SetupReportsTab()
        {
            var lblReports = new Label
            {
                Text = "التقارير المالية - سيتم تطويرها لاحقاً",
                Font = new Font("Tahoma", 14F, FontStyle.Regular),
                ForeColor = Color.FromArgb(25, 25, 112),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            tabReports.Controls.Add(lblReports);
        }

        private void SetupForm()
        {
            // Setup combo boxes
            SetupComboBoxes();

            // Event handlers
            btnAddFee.Click += BtnAddFee_Click;
            btnEditFee.Click += BtnEditFee_Click;
            btnDeleteFee.Click += BtnDeleteFee_Click;

            btnGenerateInvoice.Click += BtnGenerateInvoice_Click;
            btnViewInvoice.Click += BtnViewInvoice_Click;
            btnPayInvoice.Click += BtnPayInvoice_Click;

            btnAddPayment.Click += BtnAddPayment_Click;
            btnViewPayment.Click += BtnViewPayment_Click;
            btnPrintReceipt.Click += BtnPrintReceipt_Click;

            // Button styling
            foreach (Control control in this.Controls)
            {
                if (control is TabControl tabCtrl)
                {
                    foreach (TabPage tab in tabCtrl.TabPages)
                    {
                        foreach (Control ctrl in tab.Controls)
                        {
                            if (ctrl is Panel panel)
                            {
                                foreach (Control btnCtrl in panel.Controls)
                                {
                                    if (btnCtrl is Button btn)
                                    {
                                        btn.FlatAppearance.BorderSize = 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        private void SetupComboBoxes()
        {
            // Fee type combo
            cmbFeeType.Items.Add(new ComboBoxItem("الكل", null));
            cmbFeeType.Items.Add(new ComboBoxItem("رسوم تسجيل", FeeType.Registration));
            cmbFeeType.Items.Add(new ComboBoxItem("رسوم شهرية", FeeType.Monthly));
            cmbFeeType.Items.Add(new ComboBoxItem("رسوم نقل", FeeType.Transportation));
            cmbFeeType.Items.Add(new ComboBoxItem("رسوم كتب", FeeType.Books));
            cmbFeeType.SelectedIndex = 0;

            // Fee level combo
            cmbFeeLevel.Items.Add(new ComboBoxItem("الكل", null));
            cmbFeeLevel.Items.Add(new ComboBoxItem("رياض الأطفال", EducationLevel.Kindergarten));
            cmbFeeLevel.Items.Add(new ComboBoxItem("ابتدائي", EducationLevel.Elementary));
            cmbFeeLevel.Items.Add(new ComboBoxItem("إعدادي", EducationLevel.Middle));
            cmbFeeLevel.Items.Add(new ComboBoxItem("ثانوي", EducationLevel.High));
            cmbFeeLevel.SelectedIndex = 0;

            // Invoice status combo
            cmbInvoiceStatus.Items.Add(new ComboBoxItem("الكل", null));
            cmbInvoiceStatus.Items.Add(new ComboBoxItem("مُصدرة", InvoiceStatus.Issued));
            cmbInvoiceStatus.Items.Add(new ComboBoxItem("مدفوعة جزئياً", InvoiceStatus.PartiallyPaid));
            cmbInvoiceStatus.Items.Add(new ComboBoxItem("مدفوعة بالكامل", InvoiceStatus.FullyPaid));
            cmbInvoiceStatus.Items.Add(new ComboBoxItem("متأخرة", InvoiceStatus.Overdue));
            cmbInvoiceStatus.SelectedIndex = 0;

            // Payment method combo
            cmbPaymentMethod.Items.Add(new ComboBoxItem("الكل", null));
            cmbPaymentMethod.Items.Add(new ComboBoxItem("نقداً", PaymentMethod.Cash));
            cmbPaymentMethod.Items.Add(new ComboBoxItem("شيك", PaymentMethod.Check));
            cmbPaymentMethod.Items.Add(new ComboBoxItem("تحويل بنكي", PaymentMethod.BankTransfer));
            cmbPaymentMethod.SelectedIndex = 0;
        }

        private void SetupFeesDataGridView()
        {
            dgvFees.Columns.Clear();
            dgvFees.Columns.Add("FeeId", "المعرف");
            dgvFees.Columns.Add("FeeName", "اسم الرسم");
            dgvFees.Columns.Add("Type", "النوع");
            dgvFees.Columns.Add("Amount", "المبلغ");
            dgvFees.Columns.Add("Level", "المستوى");
            dgvFees.Columns.Add("IsActive", "نشط");
            dgvFees.Columns["FeeId"].Visible = false;
        }

        private void SetupInvoicesDataGridView()
        {
            dgvInvoices.Columns.Clear();
            dgvInvoices.Columns.Add("InvoiceId", "المعرف");
            dgvInvoices.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            dgvInvoices.Columns.Add("StudentName", "اسم الطالب");
            dgvInvoices.Columns.Add("TotalAmount", "المبلغ الإجمالي");
            dgvInvoices.Columns.Add("PaidAmount", "المبلغ المدفوع");
            dgvInvoices.Columns.Add("RemainingAmount", "المبلغ المتبقي");
            dgvInvoices.Columns.Add("Status", "الحالة");
            dgvInvoices.Columns.Add("DueDate", "تاريخ الاستحقاق");
            dgvInvoices.Columns["InvoiceId"].Visible = false;
        }

        private void SetupPaymentsDataGridView()
        {
            dgvPayments.Columns.Clear();
            dgvPayments.Columns.Add("PaymentId", "المعرف");
            dgvPayments.Columns.Add("PaymentNumber", "رقم الدفعة");
            dgvPayments.Columns.Add("StudentName", "اسم الطالب");
            dgvPayments.Columns.Add("Amount", "المبلغ");
            dgvPayments.Columns.Add("PaymentDate", "تاريخ الدفع");
            dgvPayments.Columns.Add("Method", "طريقة الدفع");
            dgvPayments.Columns.Add("PayerName", "اسم الدافع");
            dgvPayments.Columns.Add("Status", "الحالة");
            dgvPayments.Columns["PaymentId"].Visible = false;
        }

        private async void LoadData()
        {
            try
            {
                // Load fees
                var fees = await _financialService.GetAllFeesAsync();
                // Populate dgvFees with fees data

                // Load invoices
                var invoices = await _financialService.GetAllInvoicesAsync();
                // Populate dgvInvoices with invoices data

                // Load payments
                var payments = await _financialService.GetAllPaymentsAsync();
                // Populate dgvPayments with payments data
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Event handlers
        private void BtnAddFee_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة رسم جديد", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnEditFee_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة تعديل الرسم", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnDeleteFee_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم حذف الرسم المحدد", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnGenerateInvoice_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إنشاء فاتورة جديدة", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnViewInvoice_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم عرض تفاصيل الفاتورة", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnPayInvoice_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة دفع الفاتورة", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnAddPayment_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة تسجيل دفعة جديدة", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnViewPayment_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم عرض تفاصيل الدفعة", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnPrintReceipt_Click(object sender, EventArgs e)
        {
            MessageBox.Show("سيتم طباعة إيصال الدفع", "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
