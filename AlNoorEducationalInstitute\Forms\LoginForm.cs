using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة تسجيل الدخول
    /// Login form
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly IAuthenticationService _authenticationService;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnExit;
        private Label lblTitle;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblMessage;
        private PictureBox picLogo;
        private Panel pnlMain;
        private Panel pnlLogin;

        public LoginForm(IAuthenticationService authenticationService)
        {
            _authenticationService = authenticationService;
            InitializeComponent();
            SetupForm();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "نظام إدارة مؤسسة النور التربوي - تسجيل الدخول";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);

            // Main panel
            pnlMain = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 248, 255)
            };

            // Logo
            picLogo = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(210, 30),
                SizeMode = PictureBoxSizeMode.StretchImage,
                BackColor = Color.FromArgb(70, 130, 180),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Title
            lblTitle = new Label
            {
                Text = "مؤسسة النور التربوي",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 25, 112),
                Size = new Size(300, 30),
                Location = new Point(100, 120),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Login panel
            pnlLogin = new Panel
            {
                Size = new Size(300, 180),
                Location = new Point(100, 160),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Username label
            lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                Size = new Size(100, 25),
                Location = new Point(180, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Username textbox
            txtUsername = new TextBox
            {
                Font = new Font("Tahoma", 10F),
                Size = new Size(200, 25),
                Location = new Point(50, 20),
                RightToLeft = RightToLeft.No
            };

            // Password label
            lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                Size = new Size(100, 25),
                Location = new Point(180, 60),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Password textbox
            txtPassword = new TextBox
            {
                Font = new Font("Tahoma", 10F),
                Size = new Size(200, 25),
                Location = new Point(50, 60),
                UseSystemPasswordChar = true,
                RightToLeft = RightToLeft.No
            };

            // Login button
            btnLogin = new Button
            {
                Text = "دخول",
                Font = new Font("Tahoma", 10F, FontStyle.Bold),
                Size = new Size(80, 35),
                Location = new Point(170, 110),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };

            // Exit button
            btnExit = new Button
            {
                Text = "خروج",
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                Size = new Size(80, 35),
                Location = new Point(80, 110),
                BackColor = Color.FromArgb(220, 220, 220),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };

            // Message label
            lblMessage = new Label
            {
                Text = "",
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                ForeColor = Color.Red,
                Size = new Size(400, 20),
                Location = new Point(50, 350),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Add controls to login panel
            pnlLogin.Controls.Add(lblUsername);
            pnlLogin.Controls.Add(txtUsername);
            pnlLogin.Controls.Add(lblPassword);
            pnlLogin.Controls.Add(txtPassword);
            pnlLogin.Controls.Add(btnLogin);
            pnlLogin.Controls.Add(btnExit);

            // Add controls to main panel
            pnlMain.Controls.Add(picLogo);
            pnlMain.Controls.Add(lblTitle);
            pnlMain.Controls.Add(pnlLogin);
            pnlMain.Controls.Add(lblMessage);

            // Add main panel to form
            this.Controls.Add(pnlMain);

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // Event handlers
            btnLogin.Click += BtnLogin_Click;
            btnExit.Click += BtnExit_Click;
            txtPassword.KeyPress += TxtPassword_KeyPress;
            txtUsername.KeyPress += TxtUsername_KeyPress;

            // Set focus to username textbox
            this.Load += (s, e) => txtUsername.Focus();

            // Add some styling
            btnLogin.FlatAppearance.BorderSize = 0;
            btnExit.FlatAppearance.BorderSize = 0;

            // Add hover effects
            btnLogin.MouseEnter += (s, e) => btnLogin.BackColor = Color.FromArgb(100, 149, 237);
            btnLogin.MouseLeave += (s, e) => btnLogin.BackColor = Color.FromArgb(70, 130, 180);

            btnExit.MouseEnter += (s, e) => btnExit.BackColor = Color.FromArgb(200, 200, 200);
            btnExit.MouseLeave += (s, e) => btnExit.BackColor = Color.FromArgb(220, 220, 220);
        }

        private void TxtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
                e.Handled = true;
            }
        }

        private void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnLogin_Click(sender, e);
                e.Handled = true;
            }
        }

        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            await PerformLoginAsync();
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private async Task PerformLoginAsync()
        {
            try
            {
                // Clear previous message
                lblMessage.Text = "";
                lblMessage.ForeColor = Color.Red;

                // Validate input
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    ShowMessage("يرجى إدخال اسم المستخدم", Color.Red);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    ShowMessage("يرجى إدخال كلمة المرور", Color.Red);
                    txtPassword.Focus();
                    return;
                }

                // Disable controls during login
                SetControlsEnabled(false);
                ShowMessage("جاري تسجيل الدخول...", Color.Blue);

                // Perform authentication
                var result = await _authenticationService.LoginAsync(
                    txtUsername.Text.Trim(),
                    txtPassword.Text,
                    GetClientIPAddress(),
                    GetUserAgent()
                );

                if (result.Success)
                {
                    ShowMessage("تم تسجيل الدخول بنجاح", Color.Green);
                    
                    // Store current user information
                    CurrentUser.User = result.User;

                    // Check if password must be changed
                    if (result.MustChangePassword)
                    {
                        ShowMessage("يجب تغيير كلمة المرور", Color.Orange);
                        // TODO: Show change password form
                        return;
                    }

                    // Hide login form and show main form
                    this.Hide();
                    Program.ShowMainForm();
                }
                else
                {
                    ShowMessage(result.ErrorMessage ?? "فشل في تسجيل الدخول", Color.Red);
                    
                    if (result.IsAccountLocked)
                    {
                        ShowMessage($"الحساب مقفل حتى {result.LockoutEndTime:yyyy-MM-dd HH:mm}", Color.Red);
                    }
                    else if (result.RemainingAttempts > 0)
                    {
                        ShowMessage($"المحاولات المتبقية: {result.RemainingAttempts}", Color.Orange);
                    }

                    // Clear password field
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"خطأ في النظام: {ex.Message}", Color.Red);
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void ShowMessage(string message, Color color)
        {
            lblMessage.Text = message;
            lblMessage.ForeColor = color;
        }

        private void SetControlsEnabled(bool enabled)
        {
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            btnLogin.Enabled = enabled;
            btnExit.Enabled = enabled;
        }

        private string GetClientIPAddress()
        {
            // For desktop application, this would typically be the local machine IP
            return "127.0.0.1";
        }

        private string GetUserAgent()
        {
            return $"AlNoor Desktop App v1.0 - {Environment.OSVersion}";
        }
    }

    /// <summary>
    /// فئة لحفظ معلومات المستخدم الحالي
    /// Current user information holder
    /// </summary>
    public static class CurrentUser
    {
        public static User? User { get; set; }
        
        public static bool IsLoggedIn => User != null;
        
        public static bool HasRole(UserRole role) => User?.Role == role;
        
        public static bool IsInRole(params UserRole[] roles)
        {
            return User != null && Array.Exists(roles, role => role == User.Role);
        }
    }
}
