using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// النافذة الرئيسية للتطبيق
    /// Main application form
    /// </summary>
    public partial class MainForm : Form
    {
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel lblStatus;
        private ToolStripStatusLabel lblUser;
        private ToolStripStatusLabel lblTime;
        private Panel pnlMain;
        private Label lblWelcome;
        private Timer timerStatus;

        public MainForm()
        {
            InitializeComponent();
            SetupForm();
            SetupMenu();
            SetupStatusBar();
            SetupTimer();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "نظام إدارة مؤسسة النور التربوي";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Menu strip
            menuStrip = new MenuStrip
            {
                Font = new Font("Tahoma", 10F, FontStyle.Regular),
                BackColor = Color.FromArgb(70, 130, 180),
                ForeColor = Color.White,
                RightToLeft = RightToLeft.Yes
            };

            // Status strip
            statusStrip = new StatusStrip
            {
                Font = new Font("Tahoma", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(240, 240, 240),
                RightToLeft = RightToLeft.Yes
            };

            // Status labels
            lblStatus = new ToolStripStatusLabel
            {
                Text = "جاهز",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            lblUser = new ToolStripStatusLabel
            {
                Text = $"المستخدم: {CurrentUser.User?.FullName ?? "غير محدد"}",
                BorderSides = ToolStripStatusLabelBorderSides.Left
            };

            lblTime = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"),
                BorderSides = ToolStripStatusLabelBorderSides.Left
            };

            statusStrip.Items.AddRange(new ToolStripItem[] { lblStatus, lblUser, lblTime });

            // Main panel
            pnlMain = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(20)
            };

            // Welcome label
            lblWelcome = new Label
            {
                Text = $"مرحباً بك في نظام إدارة مؤسسة النور التربوي\n\nالمستخدم: {CurrentUser.User?.FullName}\nالدور: {GetRoleDisplayName(CurrentUser.User?.Role ?? UserRole.Guest)}",
                Font = new Font("Tahoma", 14F, FontStyle.Regular),
                ForeColor = Color.FromArgb(25, 25, 112),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            pnlMain.Controls.Add(lblWelcome);

            // Add controls to form
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(pnlMain);
            this.Controls.Add(statusStrip);
            this.Controls.Add(menuStrip);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void SetupForm()
        {
            this.FormClosing += MainForm_FormClosing;
            this.Load += MainForm_Load;
        }

        private void SetupMenu()
        {
            // File menu
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add(CreateMenuItem("لوحة المعلومات التنفيذية", "dashboard", MenuDashboard_Click));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(CreateMenuItem("خروج", "exit", MenuExit_Click));

            // Students menu
            var studentsMenu = new ToolStripMenuItem("الطلاب");
            studentsMenu.DropDownItems.Add(CreateMenuItem("إدارة الطلاب", "students", MenuStudents_Click));
            studentsMenu.DropDownItems.Add(CreateMenuItem("إضافة طالب جديد", "add_student", MenuAddStudent_Click));
            studentsMenu.DropDownItems.Add(CreateMenuItem("البحث عن طالب", "search_student", MenuSearchStudent_Click));

            // Employees menu
            var employeesMenu = new ToolStripMenuItem("الموظفين");
            employeesMenu.DropDownItems.Add(CreateMenuItem("إدارة الموظفين", "employees", MenuEmployees_Click));
            employeesMenu.DropDownItems.Add(CreateMenuItem("إضافة موظف جديد", "add_employee", MenuAddEmployee_Click));

            // Classes menu
            var classesMenu = new ToolStripMenuItem("الفصول الدراسية");
            classesMenu.DropDownItems.Add(CreateMenuItem("إدارة الفصول", "classes", MenuClasses_Click));
            classesMenu.DropDownItems.Add(CreateMenuItem("إضافة فصل جديد", "add_class", MenuAddClass_Click));

            // Subjects menu
            var subjectsMenu = new ToolStripMenuItem("المواد الدراسية");
            subjectsMenu.DropDownItems.Add(CreateMenuItem("إدارة المواد", "subjects", MenuSubjects_Click));
            subjectsMenu.DropDownItems.Add(CreateMenuItem("إضافة مادة جديدة", "add_subject", MenuAddSubject_Click));

            // Users menu (admin only)
            var usersMenu = new ToolStripMenuItem("المستخدمين");
            usersMenu.DropDownItems.Add(CreateMenuItem("إدارة المستخدمين", "users", MenuUsers_Click));
            usersMenu.DropDownItems.Add(CreateMenuItem("إضافة مستخدم جديد", "add_user", MenuAddUser_Click));

            // Academic menu
            var academicMenu = new ToolStripMenuItem("النظام الأكاديمي");
            academicMenu.DropDownItems.Add(CreateMenuItem("إدارة الدرجات والحضور", "academic_management", MenuAcademicManagement_Click));
            academicMenu.DropDownItems.Add(CreateMenuItem("كشوف النقاط", "student_reports", MenuStudentReports_Click));
            academicMenu.DropDownItems.Add(CreateMenuItem("الإحصائيات الأكاديمية", "academic_statistics", MenuAcademicStatistics_Click));

            // Financial menu
            var financialMenu = new ToolStripMenuItem("النظام المالي");
            financialMenu.DropDownItems.Add(CreateMenuItem("إدارة الرسوم والفواتير", "financial_management", MenuFinancialManagement_Click));
            financialMenu.DropDownItems.Add(CreateMenuItem("تسجيل الدفعات", "payments", MenuPayments_Click));
            financialMenu.DropDownItems.Add(CreateMenuItem("التقارير المالية", "financial_reports", MenuFinancialReports_Click));

            // Reports menu
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add(CreateMenuItem("منشئ التقارير المخصصة", "custom_reports", MenuCustomReports_Click));
            reportsMenu.DropDownItems.Add(CreateMenuItem("التقارير التحليلية", "analytical_reports", MenuAnalyticalReports_Click));
            reportsMenu.DropDownItems.Add(new ToolStripSeparator());
            reportsMenu.DropDownItems.Add(CreateMenuItem("تقرير الطلاب", "student_report", MenuStudentReport_Click));
            reportsMenu.DropDownItems.Add(CreateMenuItem("تقرير الموظفين", "employee_report", MenuEmployeeReport_Click));

            // Integration menu
            var integrationMenu = new ToolStripMenuItem("التكامل");
            integrationMenu.DropDownItems.Add(CreateMenuItem("إدارة الرسائل القصيرة", "sms_management", MenuSmsManagement_Click));
            integrationMenu.DropDownItems.Add(CreateMenuItem("إدارة الدفع الإلكتروني", "payment_management", MenuPaymentManagement_Click));
            integrationMenu.DropDownItems.Add(new ToolStripSeparator());
            integrationMenu.DropDownItems.Add(CreateMenuItem("إعدادات التكامل", "integration_settings", MenuIntegrationSettings_Click));

            // Tools menu
            var toolsMenu = new ToolStripMenuItem("أدوات");
            toolsMenu.DropDownItems.Add(CreateMenuItem("النسخ الاحتياطي", "backup", MenuBackup_Click));
            toolsMenu.DropDownItems.Add(CreateMenuItem("الإعدادات", "settings", MenuSettings_Click));

            // Help menu
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add(CreateMenuItem("حول البرنامج", "about", MenuAbout_Click));

            // Add menus to menu strip
            menuStrip.Items.AddRange(new ToolStripItem[]
            {
                fileMenu,
                studentsMenu,
                employeesMenu,
                classesMenu,
                subjectsMenu,
                academicMenu,
                financialMenu,
                integrationMenu,
                usersMenu,
                reportsMenu,
                toolsMenu,
                helpMenu
            });

            // Apply permissions
            ApplyMenuPermissions();
        }

        private ToolStripMenuItem CreateMenuItem(string text, string name, EventHandler clickHandler)
        {
            var menuItem = new ToolStripMenuItem(text)
            {
                Name = name,
                ForeColor = Color.White
            };
            menuItem.Click += clickHandler;
            return menuItem;
        }

        private void ApplyMenuPermissions()
        {
            var userRole = CurrentUser.User?.Role ?? UserRole.Guest;

            // Hide admin-only menus for non-admin users
            if (userRole != UserRole.SuperAdmin && userRole != UserRole.Admin)
            {
                var usersMenu = menuStrip.Items.Find("users", true);
                if (usersMenu.Length > 0)
                {
                    usersMenu[0].Visible = false;
                }
            }

            // Apply other role-based restrictions as needed
        }

        private void SetupStatusBar()
        {
            // Status bar is already set up in InitializeComponent
        }

        private void SetupTimer()
        {
            timerStatus = new Timer
            {
                Interval = 1000 // Update every second
            };
            timerStatus.Tick += TimerStatus_Tick;
            timerStatus.Start();
        }

        private void TimerStatus_Tick(object sender, EventArgs e)
        {
            lblTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            lblStatus.Text = "تم تحميل النظام بنجاح";
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إغلاق النظام؟",
                "تأكيد الإغلاق",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
            else
            {
                // Logout current user
                if (CurrentUser.IsLoggedIn)
                {
                    // TODO: Call logout service
                }
                Program.Shutdown();
            }
        }

        private string GetRoleDisplayName(UserRole role)
        {
            return role switch
            {
                UserRole.SuperAdmin => "مدير النظام الرئيسي",
                UserRole.Admin => "مدير النظام",
                UserRole.Director => "مدير المؤسسة",
                UserRole.ViceDirector => "نائب المدير",
                UserRole.Teacher => "مدرس",
                UserRole.Administrator => "موظف إداري",
                UserRole.Accountant => "محاسب",
                UserRole.Receptionist => "موظف استقبال",
                UserRole.Librarian => "أمين مكتبة",
                UserRole.ITSpecialist => "أخصائي تقنية معلومات",
                UserRole.Counselor => "مرشد طلابي",
                UserRole.ReadOnly => "قراءة فقط",
                UserRole.Guest => "ضيف",
                _ => "غير محدد"
            };
        }

        // Menu event handlers
        private void MenuDashboard_Click(object sender, EventArgs e)
        {
            try
            {
                var dashboardService = Program.ServiceProvider.GetRequiredService<IDashboardService>();
                var logger = Program.ServiceProvider.GetRequiredService<ILogger<ExecutiveDashboardForm>>();
                var form = new ExecutiveDashboardForm(dashboardService, logger);
                form.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح لوحة المعلومات التنفيذية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MenuExit_Click(object sender, EventArgs e) => this.Close();

        private void MenuStudents_Click(object sender, EventArgs e)
        {
            try
            {
                var studentService = Program.ServiceProvider.GetRequiredService<IStudentService>();
                var form = new StudentManagementForm(studentService);
                form.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إدارة الطلاب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MenuAddStudent_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إضافة طالب جديد");
        private void MenuSearchStudent_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة البحث عن طالب");

        private void MenuEmployees_Click(object sender, EventArgs e)
        {
            try
            {
                var employeeService = Program.ServiceProvider.GetRequiredService<IEmployeeService>();
                var form = new EmployeeManagementForm(employeeService);
                form.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إدارة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MenuAddEmployee_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إضافة موظف جديد");

        private void MenuClasses_Click(object sender, EventArgs e)
        {
            try
            {
                var classService = Program.ServiceProvider.GetRequiredService<IClassService>();
                var form = new ClassManagementForm(classService);
                form.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إدارة الفصول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MenuAddClass_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إضافة فصل جديد");
        private void MenuSubjects_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إدارة المواد");
        private void MenuAddSubject_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إضافة مادة جديدة");

        private void MenuAcademicManagement_Click(object sender, EventArgs e)
        {
            try
            {
                var academicService = Program.ServiceProvider.GetRequiredService<IAcademicService>();
                var studentService = Program.ServiceProvider.GetRequiredService<IStudentService>();
                var classService = Program.ServiceProvider.GetRequiredService<IClassService>();
                var subjectService = Program.ServiceProvider.GetRequiredService<ISubjectService>();
                var form = new AcademicManagementForm(academicService, studentService, classService, subjectService);
                form.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة النظام الأكاديمي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MenuStudentReports_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة كشوف النقاط");
        private void MenuAcademicStatistics_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة الإحصائيات الأكاديمية");

        private void MenuFinancialManagement_Click(object sender, EventArgs e)
        {
            try
            {
                var financialService = Program.ServiceProvider.GetRequiredService<IFinancialService>();
                var studentService = Program.ServiceProvider.GetRequiredService<IStudentService>();
                var form = new FinancialManagementForm(financialService, studentService);
                form.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة النظام المالي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MenuPayments_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة تسجيل الدفعات");
        private void MenuFinancialReports_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة التقارير المالية");

        private void MenuUsers_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إدارة المستخدمين");
        private void MenuAddUser_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إضافة مستخدم جديد");

        private void MenuCustomReports_Click(object sender, EventArgs e)
        {
            try
            {
                var reportService = Program.ServiceProvider.GetRequiredService<IAdvancedReportService>();
                var logger = Program.ServiceProvider.GetRequiredService<ILogger<CustomReportBuilderForm>>();
                var form = new CustomReportBuilderForm(reportService, logger);
                form.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح منشئ التقارير المخصصة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MenuAnalyticalReports_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة التقارير التحليلية");
        private void MenuStudentReport_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح تقرير الطلاب");
        private void MenuEmployeeReport_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح تقرير الموظفين");

        private void MenuSmsManagement_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إدارة الرسائل القصيرة");
        private void MenuPaymentManagement_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إدارة الدفع الإلكتروني");
        private void MenuIntegrationSettings_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة إعدادات التكامل");

        private void MenuBackup_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة النسخ الاحتياطي");
        private void MenuSettings_Click(object sender, EventArgs e) => ShowMessage("سيتم فتح نافذة الإعدادات");
        private void MenuAbout_Click(object sender, EventArgs e)
        {
            MessageBox.Show(
                "نظام إدارة مؤسسة النور التربوي\nالإصدار 1.0\n\nحقوق الطبع والنشر © 2025",
                "حول البرنامج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private void ShowMessage(string message)
        {
            lblStatus.Text = message;
            MessageBox.Show(message, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
