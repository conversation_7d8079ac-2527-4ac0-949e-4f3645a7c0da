using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات الحضور والغياب
    /// Attendance data model
    /// </summary>
    public class Attendance
    {
        /// <summary>
        /// المعرف الفريد لسجل الحضور
        /// </summary>
        public int AttendanceId { get; set; }

        /// <summary>
        /// معرف الطالب
        /// </summary>
        [Required(ErrorMessage = "معرف الطالب مطلوب")]
        public int StudentId { get; set; }

        /// <summary>
        /// معرف الفصل الدراسي
        /// </summary>
        [Required(ErrorMessage = "معرف الفصل مطلوب")]
        public int ClassId { get; set; }

        /// <summary>
        /// معرف المادة (اختياري - للحضور في مادة معينة)
        /// </summary>
        public int? SubjectId { get; set; }

        /// <summary>
        /// تاريخ الحضور
        /// </summary>
        [Required(ErrorMessage = "تاريخ الحضور مطلوب")]
        public DateTime AttendanceDate { get; set; }

        /// <summary>
        /// نوع الحضور (يوم كامل، حصة واحدة)
        /// </summary>
        [Required(ErrorMessage = "نوع الحضور مطلوب")]
        public AttendanceType Type { get; set; }

        /// <summary>
        /// حالة الحضور
        /// </summary>
        [Required(ErrorMessage = "حالة الحضور مطلوبة")]
        public AttendanceStatus Status { get; set; }

        /// <summary>
        /// وقت الوصول (للحضور)
        /// </summary>
        public TimeSpan? ArrivalTime { get; set; }

        /// <summary>
        /// وقت المغادرة (للانصراف)
        /// </summary>
        public TimeSpan? DepartureTime { get; set; }

        /// <summary>
        /// رقم الحصة (إذا كان الحضور لحصة معينة)
        /// </summary>
        [Range(1, 10, ErrorMessage = "رقم الحصة يجب أن يكون بين 1 و 10")]
        public int? PeriodNumber { get; set; }

        /// <summary>
        /// سبب الغياب (في حالة الغياب)
        /// </summary>
        [StringLength(200, ErrorMessage = "سبب الغياب يجب أن يكون أقل من 200 حرف")]
        public string? AbsenceReason { get; set; }

        /// <summary>
        /// هل الغياب مبرر؟
        /// </summary>
        public bool IsExcused { get; set; } = false;

        /// <summary>
        /// مرفق يبرر الغياب (شهادة طبية، إلخ)
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف المرفق يجب أن يكون أقل من 500 حرف")]
        public string? ExcuseDocument { get; set; }

        /// <summary>
        /// الفصل الدراسي
        /// </summary>
        [Required(ErrorMessage = "الفصل الدراسي مطلوب")]
        public Semester Semester { get; set; }

        /// <summary>
        /// العام الدراسي
        /// </summary>
        [Required(ErrorMessage = "العام الدراسي مطلوب")]
        [StringLength(20, ErrorMessage = "العام الدراسي يجب أن يكون أقل من 20 حرف")]
        public string AcademicYear { get; set; } = string.Empty;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual Student? Student { get; set; }
        public virtual Class? Class { get; set; }
        public virtual Subject? Subject { get; set; }
    }

    /// <summary>
    /// تعداد نوع الحضور
    /// </summary>
    public enum AttendanceType
    {
        FullDay = 1,        // يوم كامل
        SinglePeriod = 2,   // حصة واحدة
        HalfDay = 3,        // نصف يوم
        Custom = 99         // مخصص
    }

    /// <summary>
    /// تعداد حالة الحضور
    /// </summary>
    public enum AttendanceStatus
    {
        Present = 1,        // حاضر
        Absent = 2,         // غائب
        Late = 3,           // متأخر
        ExcusedAbsent = 4,  // غائب بعذر
        EarlyDeparture = 5  // انصراف مبكر
    }
}
