using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models.Backup
{
    /// <summary>
    /// نموذج النسخة الاحتياطية
    /// Backup model
    /// </summary>
    public class BackupInfo
    {
        public int BackupId { get; set; }
        
        [Required(ErrorMessage = "اسم النسخة الاحتياطية مطلوب")]
        public string BackupName { get; set; } = string.Empty;
        
        public string Description { get; set; } = string.Empty;
        public BackupType Type { get; set; }
        public BackupStatus Status { get; set; } = BackupStatus.Pending;
        
        public DateTime CreatedDate { get; set; }
        public DateTime? StartedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        
        public string FilePath { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string FileHash { get; set; } = string.Empty;
        
        public BackupLocation Location { get; set; }
        public string LocationPath { get; set; } = string.Empty;
        public string CloudProvider { get; set; } = string.Empty;
        public string CloudPath { get; set; } = string.Empty;
        
        public bool IsEncrypted { get; set; } = true;
        public string EncryptionMethod { get; set; } = "AES-256";
        public bool IsCompressed { get; set; } = true;
        public string CompressionMethod { get; set; } = "ZIP";
        
        public int CreatedByUserId { get; set; }
        public string CreatedByUserName { get; set; } = string.Empty;
        
        public TimeSpan Duration { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string LogFilePath { get; set; } = string.Empty;
        
        public bool IsAutomatic { get; set; } = false;
        public string ScheduleExpression { get; set; } = string.Empty;
        public DateTime? NextScheduledDate { get; set; }
        
        public int RetentionDays { get; set; } = 30;
        public DateTime? ExpiryDate { get; set; }
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;
        
        public List<BackupTable> Tables { get; set; } = new();
        public BackupStatistics Statistics { get; set; } = new();
    }

    /// <summary>
    /// نموذج جدول النسخة الاحتياطية
    /// Backup table model
    /// </summary>
    public class BackupTable
    {
        public string TableName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public bool IsIncluded { get; set; } = true;
        public int RecordCount { get; set; }
        public long DataSize { get; set; }
        public DateTime LastModified { get; set; }
        public BackupTableStatus Status { get; set; } = BackupTableStatus.Pending;
        public string ErrorMessage { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات النسخة الاحتياطية
    /// Backup statistics model
    /// </summary>
    public class BackupStatistics
    {
        public int TotalTables { get; set; }
        public int SuccessfulTables { get; set; }
        public int FailedTables { get; set; }
        public int SkippedTables { get; set; }
        
        public long TotalRecords { get; set; }
        public long BackedUpRecords { get; set; }
        public long FailedRecords { get; set; }
        
        public long TotalDataSize { get; set; }
        public long CompressedSize { get; set; }
        public decimal CompressionRatio => TotalDataSize > 0 ? (decimal)CompressedSize / TotalDataSize * 100 : 0;
        
        public TimeSpan TotalDuration { get; set; }
        public decimal AverageSpeed { get; set; } // Records per second
        
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
    }

    /// <summary>
    /// نموذج جدولة النسخ الاحتياطي
    /// Backup schedule model
    /// </summary>
    public class BackupSchedule
    {
        public int ScheduleId { get; set; }
        
        [Required(ErrorMessage = "اسم الجدولة مطلوب")]
        public string ScheduleName { get; set; } = string.Empty;
        
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        
        public BackupType BackupType { get; set; }
        public ScheduleFrequency Frequency { get; set; }
        public string CronExpression { get; set; } = string.Empty;
        
        public TimeSpan PreferredTime { get; set; }
        public DayOfWeek? PreferredDayOfWeek { get; set; }
        public int? PreferredDayOfMonth { get; set; }
        
        public DateTime NextRunDate { get; set; }
        public DateTime? LastRunDate { get; set; }
        public BackupStatus? LastRunStatus { get; set; }
        
        public BackupLocation Location { get; set; }
        public string LocationPath { get; set; } = string.Empty;
        public string CloudProvider { get; set; } = string.Empty;
        
        public int RetentionDays { get; set; } = 30;
        public int MaxBackupCount { get; set; } = 10;
        
        public bool NotifyOnSuccess { get; set; } = false;
        public bool NotifyOnFailure { get; set; } = true;
        public string NotificationEmails { get; set; } = string.Empty;
        
        public int CreatedByUserId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        
        public List<string> IncludedTables { get; set; } = new();
        public List<string> ExcludedTables { get; set; } = new();
    }

    /// <summary>
    /// نموذج استعادة النسخة الاحتياطية
    /// Backup restore model
    /// </summary>
    public class BackupRestore
    {
        public int RestoreId { get; set; }
        public int BackupId { get; set; }
        
        [Required(ErrorMessage = "اسم عملية الاستعادة مطلوب")]
        public string RestoreName { get; set; } = string.Empty;
        
        public string Description { get; set; } = string.Empty;
        public RestoreType Type { get; set; }
        public RestoreStatus Status { get; set; } = RestoreStatus.Pending;
        
        public DateTime CreatedDate { get; set; }
        public DateTime? StartedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        
        public string SourceFilePath { get; set; } = string.Empty;
        public string TargetDatabase { get; set; } = string.Empty;
        
        public bool OverwriteExistingData { get; set; } = false;
        public bool CreateBackupBeforeRestore { get; set; } = true;
        public string PreRestoreBackupPath { get; set; } = string.Empty;
        
        public int CreatedByUserId { get; set; }
        public string CreatedByUserName { get; set; } = string.Empty;
        
        public TimeSpan Duration { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string LogFilePath { get; set; } = string.Empty;
        
        public List<RestoreTable> Tables { get; set; } = new();
        public RestoreStatistics Statistics { get; set; } = new();
        
        public virtual BackupInfo? Backup { get; set; }
    }

    /// <summary>
    /// نموذج جدول الاستعادة
    /// Restore table model
    /// </summary>
    public class RestoreTable
    {
        public string TableName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public bool IsIncluded { get; set; } = true;
        public int RecordCount { get; set; }
        public int RestoredRecords { get; set; }
        public int FailedRecords { get; set; }
        public RestoreTableStatus Status { get; set; } = RestoreTableStatus.Pending;
        public string ErrorMessage { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات الاستعادة
    /// Restore statistics model
    /// </summary>
    public class RestoreStatistics
    {
        public int TotalTables { get; set; }
        public int SuccessfulTables { get; set; }
        public int FailedTables { get; set; }
        public int SkippedTables { get; set; }
        
        public long TotalRecords { get; set; }
        public long RestoredRecords { get; set; }
        public long FailedRecords { get; set; }
        
        public TimeSpan TotalDuration { get; set; }
        public decimal AverageSpeed { get; set; } // Records per second
        
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
    }

    /// <summary>
    /// نموذج إعدادات النسخ الاحتياطي
    /// Backup settings model
    /// </summary>
    public class BackupSettings
    {
        public int SettingsId { get; set; }
        
        public string DefaultBackupPath { get; set; } = string.Empty;
        public BackupLocation DefaultLocation { get; set; } = BackupLocation.Local;
        
        public bool EnableAutoBackup { get; set; } = true;
        public ScheduleFrequency AutoBackupFrequency { get; set; } = ScheduleFrequency.Daily;
        public TimeSpan AutoBackupTime { get; set; } = new TimeSpan(2, 0, 0); // 2:00 AM
        
        public int DefaultRetentionDays { get; set; } = 30;
        public int MaxBackupCount { get; set; } = 10;
        
        public bool EnableEncryption { get; set; } = true;
        public string EncryptionMethod { get; set; } = "AES-256";
        public string EncryptionKey { get; set; } = string.Empty;
        
        public bool EnableCompression { get; set; } = true;
        public string CompressionMethod { get; set; } = "ZIP";
        public int CompressionLevel { get; set; } = 6; // 0-9
        
        public bool EnableCloudBackup { get; set; } = false;
        public string CloudProvider { get; set; } = string.Empty;
        public string CloudAccessKey { get; set; } = string.Empty;
        public string CloudSecretKey { get; set; } = string.Empty;
        public string CloudBucketName { get; set; } = string.Empty;
        public string CloudRegion { get; set; } = string.Empty;
        
        public bool NotifyOnSuccess { get; set; } = false;
        public bool NotifyOnFailure { get; set; } = true;
        public string NotificationEmails { get; set; } = string.Empty;
        
        public bool EnableBackupVerification { get; set; } = true;
        public bool EnableIntegrityCheck { get; set; } = true;
        
        public DateTime CreatedDate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public int LastModifiedByUserId { get; set; }
    }

    // Enums
    public enum BackupType
    {
        Full = 1,
        Incremental = 2,
        Differential = 3,
        Custom = 4
    }

    public enum BackupStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        Failed = 4,
        Cancelled = 5,
        Scheduled = 6
    }

    public enum BackupLocation
    {
        Local = 1,
        Network = 2,
        Cloud = 3,
        USB = 4,
        FTP = 5
    }

    public enum BackupTableStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        Failed = 4,
        Skipped = 5
    }

    public enum ScheduleFrequency
    {
        Hourly = 1,
        Daily = 2,
        Weekly = 3,
        Monthly = 4,
        Custom = 5
    }

    public enum RestoreType
    {
        Full = 1,
        Selective = 2,
        PointInTime = 3
    }

    public enum RestoreStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        Failed = 4,
        Cancelled = 5
    }

    public enum RestoreTableStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        Failed = 4,
        Skipped = 5
    }
}
