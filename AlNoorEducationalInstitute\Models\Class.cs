using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات الفصل الدراسي
    /// Class data model
    /// </summary>
    public class Class
    {
        /// <summary>
        /// المعرف الفريد للفصل
        /// </summary>
        public int ClassId { get; set; }

        /// <summary>
        /// اسم الفصل (مثل: الأول ابتدائي - أ)
        /// </summary>
        [Required(ErrorMessage = "اسم الفصل مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الفصل يجب أن يكون أقل من 100 حرف")]
        public string ClassName { get; set; } = string.Empty;

        /// <summary>
        /// المستوى التعليمي
        /// </summary>
        [Required(ErrorMessage = "المستوى التعليمي مطلوب")]
        public EducationLevel Level { get; set; }

        /// <summary>
        /// الصف الدراسي (1، 2، 3، إلخ)
        /// </summary>
        [Required(ErrorMessage = "الصف الدراسي مطلوب")]
        [Range(1, 12, ErrorMessage = "الصف الدراسي يجب أن يكون بين 1 و 12")]
        public int Grade { get; set; }

        /// <summary>
        /// الشعبة (أ، ب، ج، إلخ)
        /// </summary>
        [Required(ErrorMessage = "الشعبة مطلوبة")]
        [StringLength(10, ErrorMessage = "الشعبة يجب أن تكون أقل من 10 أحرف")]
        public string Section { get; set; } = string.Empty;

        /// <summary>
        /// العام الدراسي
        /// </summary>
        [Required(ErrorMessage = "العام الدراسي مطلوب")]
        [StringLength(20, ErrorMessage = "العام الدراسي يجب أن يكون أقل من 20 حرف")]
        public string AcademicYear { get; set; } = string.Empty;

        /// <summary>
        /// الحد الأقصى لعدد الطلاب في الفصل
        /// </summary>
        [Range(1, 100, ErrorMessage = "الحد الأقصى للطلاب يجب أن يكون بين 1 و 100")]
        public int MaxStudents { get; set; } = 30;

        /// <summary>
        /// العدد الحالي للطلاب المسجلين
        /// </summary>
        public int CurrentStudentCount { get; set; } = 0;

        /// <summary>
        /// معرف المدرس المسؤول عن الفصل (رئيس الفصل)
        /// </summary>
        public int? ClassTeacherId { get; set; }

        /// <summary>
        /// رقم القاعة أو الغرفة
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم القاعة يجب أن يكون أقل من 20 حرف")]
        public string? RoomNumber { get; set; }

        /// <summary>
        /// حالة الفصل (نشط، مغلق، معلق)
        /// </summary>
        public ClassStatus Status { get; set; } = ClassStatus.Active;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual Employee? ClassTeacher { get; set; }
        public virtual ICollection<Student> Students { get; set; } = new List<Student>();
        public virtual ICollection<Subject> Subjects { get; set; } = new List<Subject>();
    }

    /// <summary>
    /// تعداد المستوى التعليمي
    /// </summary>
    public enum EducationLevel
    {
        Kindergarten = 1,   // رياض الأطفال
        Elementary = 2,     // ابتدائي
        Middle = 3,         // إعدادي/متوسط
        High = 4            // ثانوي
    }

    /// <summary>
    /// تعداد حالة الفصل
    /// </summary>
    public enum ClassStatus
    {
        Active = 1,     // نشط
        Closed = 2,     // مغلق
        Suspended = 3   // معلق
    }
}
