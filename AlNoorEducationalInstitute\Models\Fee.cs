using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات الرسوم الدراسية
    /// Fee data model
    /// </summary>
    public class Fee
    {
        /// <summary>
        /// المعرف الفريد للرسم
        /// </summary>
        public int FeeId { get; set; }

        /// <summary>
        /// اسم الرسم (رسوم تسجيل، رسوم شهرية، رسوم نقل، إلخ)
        /// </summary>
        [Required(ErrorMessage = "اسم الرسم مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الرسم يجب أن يكون أقل من 100 حرف")]
        public string FeeName { get; set; } = string.Empty;

        /// <summary>
        /// وصف الرسم
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف الرسم يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        /// <summary>
        /// نوع الرسم
        /// </summary>
        [Required(ErrorMessage = "نوع الرسم مطلوب")]
        public FeeType Type { get; set; }

        /// <summary>
        /// المبلغ الأساسي للرسم
        /// </summary>
        [Required(ErrorMessage = "مبلغ الرسم مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الرسم يجب أن يكون أكبر من الصفر")]
        public decimal Amount { get; set; }

        /// <summary>
        /// المستوى التعليمي المطبق عليه الرسم
        /// </summary>
        public EducationLevel? ApplicableLevel { get; set; }

        /// <summary>
        /// الصف الدراسي المطبق عليه الرسم (اختياري)
        /// </summary>
        public int? ApplicableGrade { get; set; }

        /// <summary>
        /// هل الرسم إجباري؟
        /// </summary>
        public bool IsMandatory { get; set; } = true;

        /// <summary>
        /// هل الرسم نشط؟
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ بداية تطبيق الرسم
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// تاريخ انتهاء تطبيق الرسم (اختياري)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// العام الدراسي المطبق عليه الرسم
        /// </summary>
        [Required(ErrorMessage = "العام الدراسي مطلوب")]
        [StringLength(20, ErrorMessage = "العام الدراسي يجب أن يكون أقل من 20 حرف")]
        public string AcademicYear { get; set; } = string.Empty;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }
    }

    /// <summary>
    /// تعداد أنواع الرسوم
    /// </summary>
    public enum FeeType
    {
        Registration = 1,       // رسوم تسجيل
        Monthly = 2,           // رسوم شهرية
        Transportation = 3,    // رسوم نقل
        Books = 4,             // رسوم كتب
        Uniform = 5,           // رسوم زي مدرسي
        Activities = 6,        // رسوم أنشطة
        Laboratory = 7,        // رسوم مختبر
        Library = 8,           // رسوم مكتبة
        Examination = 9,       // رسوم امتحانات
        Certificate = 10,      // رسوم شهادات
        Late = 11,             // رسوم تأخير
        Other = 99             // أخرى
    }
}
