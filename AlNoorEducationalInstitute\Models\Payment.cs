using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات الدفعة
    /// Payment data model
    /// </summary>
    public class Payment
    {
        /// <summary>
        /// المعرف الفريد للدفعة
        /// </summary>
        public int PaymentId { get; set; }

        /// <summary>
        /// رقم الدفعة (مُولد تلقائياً)
        /// </summary>
        [Required(ErrorMessage = "رقم الدفعة مطلوب")]
        [StringLength(50, ErrorMessage = "رقم الدفعة يجب أن يكون أقل من 50 حرف")]
        public string PaymentNumber { get; set; } = string.Empty;

        /// <summary>
        /// معرف الفاتورة المرتبطة
        /// </summary>
        [Required(ErrorMessage = "معرف الفاتورة مطلوب")]
        public int InvoiceId { get; set; }

        /// <summary>
        /// معرف الطالب
        /// </summary>
        [Required(ErrorMessage = "معرف الطالب مطلوب")]
        public int StudentId { get; set; }

        /// <summary>
        /// مبلغ الدفعة
        /// </summary>
        [Required(ErrorMessage = "مبلغ الدفعة مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ الدفعة يجب أن يكون أكبر من الصفر")]
        public decimal Amount { get; set; }

        /// <summary>
        /// تاريخ الدفع
        /// </summary>
        [Required(ErrorMessage = "تاريخ الدفع مطلوب")]
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// طريقة الدفع
        /// </summary>
        [Required(ErrorMessage = "طريقة الدفع مطلوبة")]
        public PaymentMethod Method { get; set; }

        /// <summary>
        /// رقم المرجع (رقم الشيك، رقم التحويل، إلخ)
        /// </summary>
        [StringLength(100, ErrorMessage = "رقم المرجع يجب أن يكون أقل من 100 حرف")]
        public string? ReferenceNumber { get; set; }

        /// <summary>
        /// اسم البنك (في حالة الشيك أو التحويل البنكي)
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم البنك يجب أن يكون أقل من 100 حرف")]
        public string? BankName { get; set; }

        /// <summary>
        /// تاريخ الشيك أو التحويل
        /// </summary>
        public DateTime? TransactionDate { get; set; }

        /// <summary>
        /// حالة الدفعة
        /// </summary>
        [Required(ErrorMessage = "حالة الدفعة مطلوبة")]
        public PaymentStatus Status { get; set; }

        /// <summary>
        /// اسم الشخص الذي دفع (ولي الأمر عادة)
        /// </summary>
        [Required(ErrorMessage = "اسم الدافع مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الدافع يجب أن يكون أقل من 100 حرف")]
        public string PayerName { get; set; } = string.Empty;

        /// <summary>
        /// رقم هاتف الدافع
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 رقم")]
        public string? PayerPhone { get; set; }

        /// <summary>
        /// ملاحظات على الدفعة
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// معرف الإيصال المطبوع
        /// </summary>
        [StringLength(50, ErrorMessage = "معرف الإيصال يجب أن يكون أقل من 50 حرف")]
        public string? ReceiptNumber { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual Invoice? Invoice { get; set; }
        public virtual Student? Student { get; set; }
    }

    /// <summary>
    /// تعداد طرق الدفع
    /// </summary>
    public enum PaymentMethod
    {
        Cash = 1,           // نقداً
        Check = 2,          // شيك
        BankTransfer = 3,   // تحويل بنكي
        CreditCard = 4,     // بطاقة ائتمان
        DebitCard = 5,      // بطاقة خصم
        OnlinePayment = 6,  // دفع إلكتروني
        Other = 99          // أخرى
    }

    /// <summary>
    /// تعداد حالة الدفعة
    /// </summary>
    public enum PaymentStatus
    {
        Pending = 1,        // في الانتظار
        Completed = 2,      // مكتملة
        Failed = 3,         // فاشلة
        Cancelled = 4,      // ملغاة
        Refunded = 5        // مُسترد
    }
}
