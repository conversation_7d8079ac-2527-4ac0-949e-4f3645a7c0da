using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models.Reports
{
    /// <summary>
    /// نموذج منشئ التقارير المخصصة
    /// Custom report builder model
    /// </summary>
    public class CustomReportBuilder
    {
        public int ReportId { get; set; }
        public string ReportName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ReportType Type { get; set; }
        public string DataSource { get; set; } = string.Empty;
        public List<ReportField> SelectedFields { get; set; } = new();
        public List<ReportFilter> Filters { get; set; } = new();
        public List<ReportGrouping> Groupings { get; set; } = new();
        public List<ReportSorting> Sortings { get; set; } = new();
        public ReportFormat OutputFormat { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedByUserId { get; set; }
        public bool IsPublic { get; set; }
        public bool IsTemplate { get; set; }
    }

    /// <summary>
    /// نموذج حقل التقرير
    /// Report field model
    /// </summary>
    public class ReportField
    {
        public string FieldName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public FieldType DataType { get; set; }
        public string TableName { get; set; } = string.Empty;
        public bool IsVisible { get; set; } = true;
        public int DisplayOrder { get; set; }
        public string Format { get; set; } = string.Empty;
        public AggregationType? Aggregation { get; set; }
        public int Width { get; set; } = 100;
    }

    /// <summary>
    /// نموذج فلتر التقرير
    /// Report filter model
    /// </summary>
    public class ReportFilter
    {
        public string FieldName { get; set; } = string.Empty;
        public FilterOperator Operator { get; set; }
        public string Value { get; set; } = string.Empty;
        public string Value2 { get; set; } = string.Empty; // For BETWEEN operator
        public LogicalOperator LogicalOperator { get; set; } = LogicalOperator.And;
    }

    /// <summary>
    /// نموذج تجميع التقرير
    /// Report grouping model
    /// </summary>
    public class ReportGrouping
    {
        public string FieldName { get; set; } = string.Empty;
        public GroupingOrder Order { get; set; } = GroupingOrder.Ascending;
        public int Level { get; set; }
        public bool ShowSubtotals { get; set; } = true;
    }

    /// <summary>
    /// نموذج ترتيب التقرير
    /// Report sorting model
    /// </summary>
    public class ReportSorting
    {
        public string FieldName { get; set; } = string.Empty;
        public SortOrder Order { get; set; } = SortOrder.Ascending;
        public int Priority { get; set; }
    }

    /// <summary>
    /// نموذج التقرير التحليلي
    /// Analytical report model
    /// </summary>
    public class AnalyticalReport
    {
        public int ReportId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public AnalysisType AnalysisType { get; set; }
        public DateTime GeneratedDate { get; set; }
        public string GeneratedBy { get; set; } = string.Empty;
        public List<AnalyticalSection> Sections { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public List<ReportChart> Charts { get; set; } = new();
        public ReportSummary Summary { get; set; } = new();
    }

    /// <summary>
    /// نموذج قسم التحليل
    /// Analytical section model
    /// </summary>
    public class AnalyticalSection
    {
        public string SectionName { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<AnalyticalMetric> Metrics { get; set; } = new();
        public List<AnalyticalInsight> Insights { get; set; } = new();
        public object Data { get; set; } = new();
    }

    /// <summary>
    /// نموذج المقياس التحليلي
    /// Analytical metric model
    /// </summary>
    public class AnalyticalMetric
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Unit { get; set; } = string.Empty;
        public decimal? PreviousValue { get; set; }
        public decimal? ChangePercentage { get; set; }
        public TrendDirection Trend { get; set; }
        public string Color { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج الرؤية التحليلية
    /// Analytical insight model
    /// </summary>
    public class AnalyticalInsight
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public InsightType Type { get; set; }
        public InsightPriority Priority { get; set; }
        public string Recommendation { get; set; } = string.Empty;
        public decimal Confidence { get; set; }
        public List<string> SupportingData { get; set; } = new();
    }

    /// <summary>
    /// نموذج رسم بياني للتقرير
    /// Report chart model
    /// </summary>
    public class ReportChart
    {
        public string ChartId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public ChartType Type { get; set; }
        public string DataSource { get; set; } = string.Empty;
        public string XAxisField { get; set; } = string.Empty;
        public string YAxisField { get; set; } = string.Empty;
        public string SeriesField { get; set; } = string.Empty;
        public Dictionary<string, object> Options { get; set; } = new();
        public object Data { get; set; } = new();
    }

    /// <summary>
    /// نموذج ملخص التقرير
    /// Report summary model
    /// </summary>
    public class ReportSummary
    {
        public string ExecutiveSummary { get; set; } = string.Empty;
        public List<KeyFinding> KeyFindings { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public Dictionary<string, decimal> KPIs { get; set; } = new();
        public string Conclusion { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج النتيجة الرئيسية
    /// Key finding model
    /// </summary>
    public class KeyFinding
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Impact { get; set; }
        public string Category { get; set; } = string.Empty;
        public List<string> Evidence { get; set; } = new();
    }

    /// <summary>
    /// نموذج مقارنة الأداء عبر الزمن
    /// Performance comparison over time model
    /// </summary>
    public class PerformanceComparison
    {
        public string MetricName { get; set; } = string.Empty;
        public List<TimeSeriesData> TimeSeries { get; set; } = new();
        public ComparisonPeriod Period { get; set; }
        public decimal AverageGrowthRate { get; set; }
        public decimal Volatility { get; set; }
        public TrendDirection OverallTrend { get; set; }
        public List<SeasonalPattern> SeasonalPatterns { get; set; } = new();
    }

    /// <summary>
    /// نموذج بيانات السلسلة الزمنية
    /// Time series data model
    /// </summary>
    public class TimeSeriesData
    {
        public DateTime Date { get; set; }
        public decimal Value { get; set; }
        public string Label { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// نموذج النمط الموسمي
    /// Seasonal pattern model
    /// </summary>
    public class SeasonalPattern
    {
        public string PatternName { get; set; } = string.Empty;
        public SeasonType Season { get; set; }
        public decimal AverageValue { get; set; }
        public decimal DeviationFromMean { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج تحليل الطلاب الجدد مقابل القدامى
    /// New vs returning students analysis model
    /// </summary>
    public class StudentCohortAnalysis
    {
        public string CohortName { get; set; } = string.Empty;
        public DateTime CohortStartDate { get; set; }
        public int InitialSize { get; set; }
        public int CurrentSize { get; set; }
        public decimal RetentionRate { get; set; }
        public decimal AveragePerformance { get; set; }
        public decimal AverageAttendance { get; set; }
        public List<CohortMetric> Metrics { get; set; } = new();
        public List<CohortComparison> Comparisons { get; set; } = new();
    }

    /// <summary>
    /// نموذج مقياس المجموعة
    /// Cohort metric model
    /// </summary>
    public class CohortMetric
    {
        public string MetricName { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Period { get; set; } = string.Empty;
        public decimal Benchmark { get; set; }
        public PerformanceLevel Level { get; set; }
    }

    /// <summary>
    /// نموذج مقارنة المجموعات
    /// Cohort comparison model
    /// </summary>
    public class CohortComparison
    {
        public string ComparisonName { get; set; } = string.Empty;
        public string CohortA { get; set; } = string.Empty;
        public string CohortB { get; set; } = string.Empty;
        public decimal DifferenceValue { get; set; }
        public decimal DifferencePercentage { get; set; }
        public string Significance { get; set; } = string.Empty;
    }

    // Enums
    public enum ReportType
    {
        Academic = 1,
        Financial = 2,
        Administrative = 3,
        Statistical = 4,
        Analytical = 5,
        Custom = 99
    }

    public enum FieldType
    {
        Text = 1,
        Number = 2,
        Date = 3,
        Boolean = 4,
        Currency = 5,
        Percentage = 6
    }

    public enum AggregationType
    {
        Sum = 1,
        Average = 2,
        Count = 3,
        Min = 4,
        Max = 5,
        StdDev = 6
    }

    public enum FilterOperator
    {
        Equals = 1,
        NotEquals = 2,
        GreaterThan = 3,
        LessThan = 4,
        GreaterThanOrEqual = 5,
        LessThanOrEqual = 6,
        Contains = 7,
        StartsWith = 8,
        EndsWith = 9,
        Between = 10,
        In = 11,
        IsNull = 12,
        IsNotNull = 13
    }

    public enum LogicalOperator
    {
        And = 1,
        Or = 2
    }

    public enum GroupingOrder
    {
        Ascending = 1,
        Descending = 2
    }

    public enum SortOrder
    {
        Ascending = 1,
        Descending = 2
    }

    public enum ReportFormat
    {
        Excel = 1,
        PDF = 2,
        CSV = 3,
        HTML = 4,
        JSON = 5
    }

    public enum AnalysisType
    {
        Descriptive = 1,
        Comparative = 2,
        Trend = 3,
        Predictive = 4,
        Cohort = 5
    }

    public enum TrendDirection
    {
        Up = 1,
        Down = 2,
        Stable = 3,
        Volatile = 4
    }

    public enum InsightType
    {
        Opportunity = 1,
        Risk = 2,
        Anomaly = 3,
        Pattern = 4,
        Correlation = 5
    }

    public enum InsightPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    public enum ChartType
    {
        Line = 1,
        Bar = 2,
        Pie = 3,
        Area = 4,
        Scatter = 5,
        Histogram = 6,
        Heatmap = 7
    }

    public enum ComparisonPeriod
    {
        Daily = 1,
        Weekly = 2,
        Monthly = 3,
        Quarterly = 4,
        Yearly = 5
    }

    public enum SeasonType
    {
        Spring = 1,
        Summer = 2,
        Fall = 3,
        Winter = 4,
        FirstSemester = 5,
        SecondSemester = 6
    }

    public enum PerformanceLevel
    {
        Poor = 1,
        BelowAverage = 2,
        Average = 3,
        AboveAverage = 4,
        Excellent = 5
    }
}
