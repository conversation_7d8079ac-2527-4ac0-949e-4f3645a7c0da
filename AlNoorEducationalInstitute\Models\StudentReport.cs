using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات تقرير الطالب (كشف النقاط)
    /// Student report data model
    /// </summary>
    public class StudentReport
    {
        /// <summary>
        /// المعرف الفريد للتقرير
        /// </summary>
        public int ReportId { get; set; }

        /// <summary>
        /// معرف الطالب
        /// </summary>
        [Required(ErrorMessage = "معرف الطالب مطلوب")]
        public int StudentId { get; set; }

        /// <summary>
        /// معرف الفصل الدراسي
        /// </summary>
        [Required(ErrorMessage = "معرف الفصل مطلوب")]
        public int ClassId { get; set; }

        /// <summary>
        /// الفصل الدراسي
        /// </summary>
        [Required(ErrorMessage = "الفصل الدراسي مطلوب")]
        public Semester Semester { get; set; }

        /// <summary>
        /// العام الدراسي
        /// </summary>
        [Required(ErrorMessage = "العام الدراسي مطلوب")]
        [StringLength(20, ErrorMessage = "العام الدراسي يجب أن يكون أقل من 20 حرف")]
        public string AcademicYear { get; set; } = string.Empty;

        /// <summary>
        /// المعدل العام للفصل
        /// </summary>
        [Range(0, 100, ErrorMessage = "المعدل يجب أن يكون بين 0 و 100")]
        public decimal OverallAverage { get; set; }

        /// <summary>
        /// إجمالي الدرجات المحصل عليها
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "إجمالي الدرجات يجب أن يكون أكبر من أو يساوي الصفر")]
        public decimal TotalScore { get; set; }

        /// <summary>
        /// إجمالي الدرجات العظمى
        /// </summary>
        [Range(1, double.MaxValue, ErrorMessage = "إجمالي الدرجات العظمى يجب أن يكون أكبر من الصفر")]
        public decimal TotalMaxScore { get; set; }

        /// <summary>
        /// عدد المواد المجتازة
        /// </summary>
        public int PassedSubjects { get; set; }

        /// <summary>
        /// عدد المواد الراسبة
        /// </summary>
        public int FailedSubjects { get; set; }

        /// <summary>
        /// إجمالي عدد المواد
        /// </summary>
        public int TotalSubjects { get; set; }

        /// <summary>
        /// الترتيب على الفصل
        /// </summary>
        public int? ClassRank { get; set; }

        /// <summary>
        /// إجمالي عدد طلاب الفصل
        /// </summary>
        public int? TotalClassStudents { get; set; }

        /// <summary>
        /// نسبة الحضور
        /// </summary>
        [Range(0, 100, ErrorMessage = "نسبة الحضور يجب أن تكون بين 0 و 100")]
        public decimal AttendancePercentage { get; set; }

        /// <summary>
        /// عدد أيام الحضور
        /// </summary>
        public int AttendanceDays { get; set; }

        /// <summary>
        /// عدد أيام الغياب
        /// </summary>
        public int AbsenceDays { get; set; }

        /// <summary>
        /// عدد أيام الغياب المبرر
        /// </summary>
        public int ExcusedAbsenceDays { get; set; }

        /// <summary>
        /// التقدير العام
        /// </summary>
        [StringLength(50, ErrorMessage = "التقدير يجب أن يكون أقل من 50 حرف")]
        public string? OverallGrade { get; set; }

        /// <summary>
        /// حالة النجاح/الرسوب
        /// </summary>
        [Required(ErrorMessage = "حالة النجاح مطلوبة")]
        public PassStatus Status { get; set; }

        /// <summary>
        /// ملاحظات المدرس
        /// </summary>
        [StringLength(1000, ErrorMessage = "ملاحظات المدرس يجب أن تكون أقل من 1000 حرف")]
        public string? TeacherComments { get; set; }

        /// <summary>
        /// ملاحظات الإدارة
        /// </summary>
        [StringLength(1000, ErrorMessage = "ملاحظات الإدارة يجب أن تكون أقل من 1000 حرف")]
        public string? AdministrationComments { get; set; }

        /// <summary>
        /// تاريخ إصدار التقرير
        /// </summary>
        [Required(ErrorMessage = "تاريخ الإصدار مطلوب")]
        public DateTime IssueDate { get; set; }

        /// <summary>
        /// هل تم اعتماد التقرير؟
        /// </summary>
        public bool IsApproved { get; set; } = false;

        /// <summary>
        /// تاريخ الاعتماد
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي اعتمد التقرير
        /// </summary>
        public int? ApprovedByUserId { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual Student? Student { get; set; }
        public virtual Class? Class { get; set; }
        public virtual ICollection<SubjectGrade> SubjectGrades { get; set; } = new List<SubjectGrade>();
    }

    /// <summary>
    /// نموذج درجة المادة في التقرير
    /// </summary>
    public class SubjectGrade
    {
        public int SubjectGradeId { get; set; }
        public int ReportId { get; set; }
        public int SubjectId { get; set; }
        public string SubjectName { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public decimal MaxScore { get; set; }
        public decimal Percentage { get; set; }
        public string Grade { get; set; } = string.Empty;
        public bool IsPassed { get; set; }
        public string? TeacherName { get; set; }

        // Navigation Properties
        public virtual StudentReport? Report { get; set; }
        public virtual Subject? Subject { get; set; }
    }

    /// <summary>
    /// تعداد حالة النجاح/الرسوب
    /// </summary>
    public enum PassStatus
    {
        Passed = 1,         // ناجح
        Failed = 2,         // راسب
        Conditional = 3,    // مشروط
        Incomplete = 4      // غير مكتمل
    }
}
