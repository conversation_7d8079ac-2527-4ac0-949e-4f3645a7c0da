using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات المادة الدراسية
    /// Subject data model
    /// </summary>
    public class Subject
    {
        /// <summary>
        /// المعرف الفريد للمادة
        /// </summary>
        public int SubjectId { get; set; }

        /// <summary>
        /// كود المادة (مثل: MATH101، ARAB201)
        /// </summary>
        [Required(ErrorMessage = "كود المادة مطلوب")]
        [StringLength(20, ErrorMessage = "كود المادة يجب أن يكون أقل من 20 حرف")]
        public string SubjectCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم المادة باللغة العربية
        /// </summary>
        [Required(ErrorMessage = "اسم المادة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المادة يجب أن يكون أقل من 100 حرف")]
        public string SubjectNameArabic { get; set; } = string.Empty;

        /// <summary>
        /// اسم المادة باللغة الإنجليزية (اختياري)
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم المادة الإنجليزي يجب أن يكون أقل من 100 حرف")]
        public string? SubjectNameEnglish { get; set; }

        /// <summary>
        /// وصف المادة
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف المادة يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        /// <summary>
        /// نوع المادة (أساسية، اختيارية، نشاط)
        /// </summary>
        [Required(ErrorMessage = "نوع المادة مطلوب")]
        public SubjectType Type { get; set; }

        /// <summary>
        /// المستوى التعليمي المناسب للمادة
        /// </summary>
        [Required(ErrorMessage = "المستوى التعليمي مطلوب")]
        public EducationLevel Level { get; set; }

        /// <summary>
        /// الصف الدراسي المناسب للمادة
        /// </summary>
        [Required(ErrorMessage = "الصف الدراسي مطلوب")]
        [Range(1, 12, ErrorMessage = "الصف الدراسي يجب أن يكون بين 1 و 12")]
        public int Grade { get; set; }

        /// <summary>
        /// عدد الحصص الأسبوعية
        /// </summary>
        [Range(1, 20, ErrorMessage = "عدد الحصص يجب أن يكون بين 1 و 20")]
        public int WeeklyHours { get; set; } = 1;

        /// <summary>
        /// الدرجة العظمى للمادة
        /// </summary>
        [Range(1, 1000, ErrorMessage = "الدرجة العظمى يجب أن تكون بين 1 و 1000")]
        public int MaxGrade { get; set; } = 100;

        /// <summary>
        /// درجة النجاح (الحد الأدنى للنجاح)
        /// </summary>
        [Range(1, 1000, ErrorMessage = "درجة النجاح يجب أن تكون بين 1 و 1000")]
        public int PassingGrade { get; set; } = 50;

        /// <summary>
        /// معرف المدرس المسؤول عن المادة
        /// </summary>
        public int? TeacherId { get; set; }

        /// <summary>
        /// الكتاب المدرسي المقرر
        /// </summary>
        [StringLength(200, ErrorMessage = "اسم الكتاب يجب أن يكون أقل من 200 حرف")]
        public string? TextBook { get; set; }

        /// <summary>
        /// المراجع الإضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "المراجع الإضافية يجب أن تكون أقل من 500 حرف")]
        public string? AdditionalReferences { get; set; }

        /// <summary>
        /// متطلبات المادة (المواد التي يجب دراستها قبل هذه المادة)
        /// </summary>
        [StringLength(200, ErrorMessage = "متطلبات المادة يجب أن تكون أقل من 200 حرف")]
        public string? Prerequisites { get; set; }

        /// <summary>
        /// حالة المادة (نشطة، معلقة، ملغاة)
        /// </summary>
        public SubjectStatus Status { get; set; } = SubjectStatus.Active;

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual Employee? Teacher { get; set; }
        public virtual ICollection<Class> Classes { get; set; } = new List<Class>();
    }

    /// <summary>
    /// تعداد نوع المادة
    /// </summary>
    public enum SubjectType
    {
        Core = 1,           // أساسية
        Elective = 2,       // اختيارية
        Activity = 3,       // نشاط
        Language = 4,       // لغة
        Science = 5,        // علوم
        Mathematics = 6,    // رياضيات
        SocialStudies = 7,  // دراسات اجتماعية
        Arts = 8,           // فنون
        Sports = 9,         // رياضة
        Religion = 10,      // تربية دينية
        Other = 99          // أخرى
    }

    /// <summary>
    /// تعداد حالة المادة
    /// </summary>
    public enum SubjectStatus
    {
        Active = 1,     // نشطة
        Suspended = 2,  // معلقة
        Cancelled = 3   // ملغاة
    }
}
