using System;
using System.IO;
using System.Windows.Forms;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using AlNoorEducationalInstitute.Data;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Forms;

namespace AlNoorEducationalInstitute
{
    internal static class Program
    {
        public static IServiceProvider ServiceProvider { get; private set; }
        public static IConfiguration Configuration { get; private set; }

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // Initialize configuration
                InitializeConfiguration();

                // Configure services
                ConfigureServices();

                // Initialize database
                InitializeDatabase();

                // Configure application
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.SetHighDpiMode(HighDpiMode.SystemAware);

                // Start the application with login form
                var loginForm = ServiceProvider.GetRequiredService<LoginForm>();
                Application.Run(loginForm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void InitializeConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            Configuration = builder.Build();
        }

        private static void ConfigureServices()
        {
            var services = new ServiceCollection();

            // Add configuration
            services.AddSingleton(Configuration);

            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConfiguration(Configuration.GetSection("Logging"));
                builder.AddConsole();
            });

            // Add data services
            services.AddSingleton<DatabaseManager>();
            services.AddScoped<IStudentService, StudentService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<IClassService, ClassService>();
            services.AddScoped<ISubjectService, SubjectService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IAuthenticationService, AuthenticationService>();

            // Add financial and academic services
            services.AddScoped<IFinancialService, FinancialService>();
            services.AddScoped<IAcademicService, AcademicService>();

            // Add dashboard service
            services.AddScoped<IDashboardService, DashboardService>();

            // Add advanced report service
            services.AddScoped<IAdvancedReportService, AdvancedReportService>();

            // Add integration services
            services.AddScoped<ISmsService, SmsService>();
            services.AddScoped<IOnlinePaymentService, OnlinePaymentService>();

            // Add backup service
            services.AddScoped<IBackupService, BackupService>();

            // Add forms
            services.AddTransient<LoginForm>();
            services.AddTransient<MainForm>();
            services.AddTransient<StudentManagementForm>();
            services.AddTransient<EmployeeManagementForm>();
            services.AddTransient<ClassManagementForm>();

            ServiceProvider = services.BuildServiceProvider();
        }

        private static void InitializeDatabase()
        {
            try
            {
                var databaseManager = ServiceProvider.GetRequiredService<DatabaseManager>();
                databaseManager.InitializeDatabase();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        public static void ShowMainForm()
        {
            var mainForm = ServiceProvider.GetRequiredService<MainForm>();
            mainForm.Show();
        }

        public static void Shutdown()
        {
            ServiceProvider?.Dispose();
            Application.Exit();
        }
    }
}
