using System;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة المصادقة
    /// Authentication service interface
    /// </summary>
    public interface IAuthenticationService
    {
        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        Task<AuthenticationResult> LoginAsync(string username, string password, string? ipAddress = null, string? userAgent = null);

        /// <summary>
        /// تسجيل الخروج
        /// </summary>
        Task<bool> LogoutAsync(int userId);

        /// <summary>
        /// التحقق من صحة كلمة المرور
        /// </summary>
        bool VerifyPassword(string password, string hashedPassword);

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        string HashPassword(string password);

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// </summary>
        PasswordStrengthResult ValidatePasswordStrength(string password);

        /// <summary>
        /// التحقق من انتهاء صلاحية كلمة المرور
        /// </summary>
        Task<bool> IsPasswordExpiredAsync(int userId);

        /// <summary>
        /// التحقق من قفل الحساب
        /// </summary>
        Task<bool> IsAccountLockedAsync(int userId);

        /// <summary>
        /// التحقق من صلاحية المستخدم للوصول إلى وظيفة معينة
        /// </summary>
        Task<bool> HasPermissionAsync(int userId, string permission);

        /// <summary>
        /// التحقق من دور المستخدم
        /// </summary>
        Task<bool> IsInRoleAsync(int userId, UserRole role);

        /// <summary>
        /// إنشاء رمز إعادة تعيين كلمة المرور
        /// </summary>
        Task<string> GeneratePasswordResetTokenAsync(int userId);

        /// <summary>
        /// التحقق من صحة رمز إعادة تعيين كلمة المرور
        /// </summary>
        Task<bool> ValidatePasswordResetTokenAsync(int userId, string token);

        /// <summary>
        /// تسجيل محاولة دخول في سجل الأمان
        /// </summary>
        Task LogSecurityEventAsync(SecurityEventType eventType, int? userId, string? username, string? ipAddress, string? userAgent, string? details = null);
    }

    /// <summary>
    /// نتيجة عملية المصادقة
    /// </summary>
    public class AuthenticationResult
    {
        public bool Success { get; set; }
        public User? User { get; set; }
        public string? ErrorMessage { get; set; }
        public bool IsAccountLocked { get; set; }
        public bool IsPasswordExpired { get; set; }
        public bool MustChangePassword { get; set; }
        public int RemainingAttempts { get; set; }
        public DateTime? LockoutEndTime { get; set; }
    }

    /// <summary>
    /// نتيجة فحص قوة كلمة المرور
    /// </summary>
    public class PasswordStrengthResult
    {
        public bool IsValid { get; set; }
        public int Score { get; set; } // من 0 إلى 100
        public List<string> Issues { get; set; } = new();
        public List<string> Suggestions { get; set; } = new();
    }

    /// <summary>
    /// أنواع أحداث الأمان
    /// </summary>
    public enum SecurityEventType
    {
        LoginSuccess = 1,
        LoginFailure = 2,
        Logout = 3,
        PasswordChanged = 4,
        PasswordReset = 5,
        AccountLocked = 6,
        AccountUnlocked = 7,
        PermissionDenied = 8,
        SuspiciousActivity = 9,
        DataAccess = 10,
        DataModification = 11,
        SystemAccess = 12
    }
}
