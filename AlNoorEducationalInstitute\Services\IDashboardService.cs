using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models.Dashboard;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة لوحة المعلومات التنفيذية
    /// Executive dashboard service interface
    /// </summary>
    public interface IDashboardService
    {
        // مؤشرات الأداء الرئيسية
        Task<DashboardKPIs> GetDashboardKPIsAsync(DashboardFilter? filter = null);
        Task<IEnumerable<QuickStats>> GetQuickStatsAsync(DashboardFilter? filter = null);
        
        // توزيع الطلاب
        Task<IEnumerable<StudentDistribution>> GetStudentDistributionByLevelAsync();
        Task<IEnumerable<StudentDistribution>> GetStudentDistributionByGradeAsync();
        Task<IEnumerable<StudentDistribution>> GetStudentDistributionByClassAsync(EducationLevel? level = null);
        
        // بيانات الحضور
        Task<IEnumerable<DailyAttendance>> GetDailyAttendanceAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<DailyAttendance>> GetWeeklyAttendanceAsync(DateTime weekStart);
        Task<IEnumerable<DailyAttendance>> GetMonthlyAttendanceAsync(int year, int month);
        Task<decimal> GetCurrentAttendanceRateAsync();
        
        // الأداء المالي
        Task<IEnumerable<MonthlyFinancialPerformance>> GetMonthlyFinancialPerformanceAsync(int year);
        Task<IEnumerable<FinancialPerformanceByFeeType>> GetFinancialPerformanceByFeeTypeAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetCollectionRateAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetMonthlyRevenueAsync(int year, int month);
        Task<decimal> GetYearlyRevenueAsync(int year);
        
        // الأداء الأكاديمي
        Task<IEnumerable<ClassPerformanceComparison>> GetClassPerformanceComparisonAsync(Semester semester, string academicYear);
        Task<IEnumerable<SubjectPerformance>> GetSubjectPerformanceAsync(Semester semester, string academicYear);
        Task<IEnumerable<AcademicTrends>> GetAcademicTrendsAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetOverallAcademicAverageAsync(Semester semester, string academicYear);
        Task<decimal> GetOverallPassRateAsync(Semester semester, string academicYear);
        
        // التنبيهات والإشعارات
        Task<IEnumerable<DashboardAlert>> GetActiveAlertsAsync(int userId);
        Task<IEnumerable<DashboardAlert>> GetUnreadAlertsAsync(int userId);
        Task<int> AddAlertAsync(DashboardAlert alert);
        Task<bool> MarkAlertAsReadAsync(int alertId, int userId);
        Task<bool> DismissAlertAsync(int alertId, int userId);
        Task GenerateSystemAlertsAsync();
        
        // إعدادات لوحة المعلومات
        Task<DashboardSettings> GetDashboardSettingsAsync(int userId);
        Task<bool> SaveDashboardSettingsAsync(DashboardSettings settings);
        Task<DashboardSettings> GetDefaultDashboardSettingsAsync();
        
        // تحديث البيانات
        Task RefreshDashboardDataAsync();
        Task<DateTime> GetLastDataUpdateAsync();
        Task<bool> IsDataStaleAsync(int maxAgeMinutes = 30);
        
        // تصدير البيانات
        Task<byte[]> ExportDashboardToPdfAsync(DashboardFilter? filter = null);
        Task<byte[]> ExportKPIsToExcelAsync(DashboardFilter? filter = null);
        Task<byte[]> ExportChartsToImageAsync(string chartType, DashboardFilter? filter = null);
        
        // إحصائيات متقدمة
        Task<Dictionary<string, object>> GetAdvancedStatisticsAsync(DashboardFilter? filter = null);
        Task<IEnumerable<object>> GetCustomChartDataAsync(string chartType, DashboardFilter? filter = null);
        Task<object> GetPredictiveAnalyticsAsync(string analysisType, DashboardFilter? filter = null);
        
        // مقارنات زمنية
        Task<object> GetYearOverYearComparisonAsync(int currentYear, int previousYear);
        Task<object> GetMonthOverMonthComparisonAsync(int year, int currentMonth, int previousMonth);
        Task<object> GetSemesterComparisonAsync(string academicYear, Semester currentSemester, Semester previousSemester);
        
        // تحليلات الاتجاهات
        Task<IEnumerable<object>> GetEnrollmentTrendsAsync(int years = 5);
        Task<IEnumerable<object>> GetRevenueTrendsAsync(int months = 12);
        Task<IEnumerable<object>> GetAcademicPerformanceTrendsAsync(int semesters = 6);
        Task<IEnumerable<object>> GetAttendanceTrendsAsync(int months = 6);
        
        // تحليلات التنبؤ
        Task<object> PredictNextMonthRevenueAsync();
        Task<object> PredictStudentEnrollmentAsync(int monthsAhead = 3);
        Task<object> PredictAcademicPerformanceAsync(Semester targetSemester, string academicYear);
        
        // تقارير الأداء
        Task<object> GetPerformanceReportAsync(string reportType, DashboardFilter? filter = null);
        Task<IEnumerable<object>> GetBenchmarkingDataAsync(string benchmarkType);
        Task<object> GetEfficiencyMetricsAsync(DashboardFilter? filter = null);
        
        // إدارة البيانات التاريخية
        Task ArchiveOldDataAsync(DateTime cutoffDate);
        Task<bool> ValidateDataIntegrityAsync();
        Task RepairDataInconsistenciesAsync();
        
        // تحسين الأداء
        Task WarmupCacheAsync();
        Task ClearCacheAsync();
        Task<TimeSpan> GetAverageResponseTimeAsync();
    }
}
