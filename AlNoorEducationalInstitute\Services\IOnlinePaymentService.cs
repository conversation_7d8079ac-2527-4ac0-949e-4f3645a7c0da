using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models.Integration;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة الدفع الإلكتروني
    /// Online payment service interface
    /// </summary>
    public interface IOnlinePaymentService
    {
        // إعدادات بوابة الدفع
        Task<PaymentGatewaySettings?> GetActiveGatewaySettingsAsync();
        Task<IEnumerable<PaymentGatewaySettings>> GetAllGatewaySettingsAsync();
        Task<int> SaveGatewaySettingsAsync(PaymentGatewaySettings settings);
        Task<bool> UpdateGatewaySettingsAsync(PaymentGatewaySettings settings);
        Task<bool> DeleteGatewaySettingsAsync(int settingsId);
        Task<bool> ActivateGatewayAsync(int settingsId);
        Task<bool> TestGatewayConnectionAsync(int settingsId);

        // إنشاء المعاملات
        Task<string> CreatePaymentTransactionAsync(int invoiceId, decimal amount, string payerName, string payerEmail, string payerPhone);
        Task<string> CreatePaymentLinkAsync(int invoiceId, decimal amount, string payerName, string payerEmail, string payerPhone);
        Task<OnlinePaymentTransaction?> GetTransactionByIdAsync(int transactionId);
        Task<OnlinePaymentTransaction?> GetTransactionByNumberAsync(string transactionNumber);
        Task<IEnumerable<OnlinePaymentTransaction>> GetTransactionsByInvoiceAsync(int invoiceId);
        Task<IEnumerable<OnlinePaymentTransaction>> GetTransactionsByStudentAsync(int studentId);

        // معالجة المعاملات
        Task<bool> ProcessPaymentAsync(int transactionId);
        Task<bool> AuthorizePaymentAsync(int transactionId);
        Task<bool> CapturePaymentAsync(int transactionId);
        Task<bool> RefundPaymentAsync(int transactionId, decimal? refundAmount = null, string reason = "");
        Task<bool> CancelPaymentAsync(int transactionId, string reason = "");
        Task<bool> VoidPaymentAsync(int transactionId, string reason = "");

        // التحقق من حالة المعاملات
        Task<OnlinePaymentStatus> CheckTransactionStatusAsync(int transactionId);
        Task<bool> UpdateTransactionStatusAsync(int transactionId, OnlinePaymentStatus status, string statusMessage = "");
        Task<bool> SyncTransactionWithGatewayAsync(int transactionId);
        Task SyncAllPendingTransactionsAsync();

        // إدارة المعاملات
        Task<IEnumerable<OnlinePaymentTransaction>> GetAllTransactionsAsync();
        Task<IEnumerable<OnlinePaymentTransaction>> GetTransactionsByStatusAsync(OnlinePaymentStatus status);
        Task<IEnumerable<OnlinePaymentTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<OnlinePaymentTransaction>> GetTransactionsByGatewayAsync(PaymentGatewayType gatewayType);
        Task<IEnumerable<OnlinePaymentTransaction>> GetFailedTransactionsAsync();
        Task<IEnumerable<OnlinePaymentTransaction>> GetPendingTransactionsAsync();

        // إعادة المحاولة والاسترداد
        Task<bool> RetryFailedTransactionAsync(int transactionId);
        Task<int> RetryAllFailedTransactionsAsync();
        Task<bool> RecoverTransactionAsync(int transactionId);
        Task<IEnumerable<OnlinePaymentTransaction>> GetTransactionsForRecoveryAsync();

        // الإشعارات والتنبيهات
        Task<int> ProcessPaymentNotificationAsync(PaymentNotification notification);
        Task<IEnumerable<PaymentNotification>> GetUnprocessedNotificationsAsync();
        Task<bool> MarkNotificationAsProcessedAsync(int notificationId, string result);
        Task ProcessPendingNotificationsAsync();

        // التقارير المالية
        Task<object> GetPaymentStatisticsAsync(DateTime startDate, DateTime endDate);
        Task<object> GetRevenueReportAsync(DateTime startDate, DateTime endDate);
        Task<object> GetTransactionVolumeReportAsync(DateTime startDate, DateTime endDate);
        Task<object> GetGatewayPerformanceReportAsync(DateTime startDate, DateTime endDate);
        Task<object> GetFailureAnalysisReportAsync(DateTime startDate, DateTime endDate);

        // إدارة الرسوم والتكاليف
        Task<decimal> CalculateTransactionFeeAsync(decimal amount, PaymentGatewayType gatewayType);
        Task<decimal> GetNetAmountAsync(decimal grossAmount, PaymentGatewayType gatewayType);
        Task<object> GetFeeStructureAsync(PaymentGatewayType gatewayType);
        Task<decimal> GetTotalFeesAsync(DateTime startDate, DateTime endDate);

        // الأمان والتشفير
        Task<string> EncryptSensitiveDataAsync(string data);
        Task<string> DecryptSensitiveDataAsync(string encryptedData);
        Task<bool> ValidatePaymentSignatureAsync(string signature, string data);
        Task<string> GeneratePaymentSignatureAsync(string data);
        Task<bool> ValidateCallbackDataAsync(Dictionary<string, string> callbackData);

        // إدارة العملات
        Task<IEnumerable<string>> GetSupportedCurrenciesAsync();
        Task<decimal> ConvertCurrencyAsync(decimal amount, string fromCurrency, string toCurrency);
        Task<object> GetExchangeRatesAsync();
        Task<bool> IsCurrencySupportedAsync(string currency);

        // إدارة طرق الدفع
        Task<IEnumerable<string>> GetSupportedPaymentMethodsAsync(PaymentGatewayType gatewayType);
        Task<bool> IsPaymentMethodSupportedAsync(string paymentMethod, PaymentGatewayType gatewayType);
        Task<object> GetPaymentMethodDetailsAsync(string paymentMethod);

        // التكامل مع النظام المالي
        Task<bool> CreatePaymentRecordAsync(int transactionId);
        Task<bool> UpdateInvoiceStatusAsync(int invoiceId);
        Task<bool> ReconcilePaymentsAsync(DateTime date);
        Task<IEnumerable<object>> GetUnreconciledTransactionsAsync();

        // إدارة المخاطر والاحتيال
        Task<bool> ValidateTransactionAsync(OnlinePaymentTransaction transaction);
        Task<object> PerformFraudCheckAsync(OnlinePaymentTransaction transaction);
        Task<bool> IsTransactionSuspiciousAsync(OnlinePaymentTransaction transaction);
        Task<IEnumerable<OnlinePaymentTransaction>> GetSuspiciousTransactionsAsync();

        // التدقيق والامتثال
        Task<IEnumerable<IntegrationLog>> GetPaymentLogsAsync(DateTime startDate, DateTime endDate);
        Task LogPaymentOperationAsync(string operation, string requestData, string responseData, bool isSuccess, string errorMessage = "");
        Task<object> GetComplianceReportAsync(DateTime startDate, DateTime endDate);
        Task<bool> ValidateComplianceAsync();

        // إدارة الجلسات والتوكنات
        Task<string> CreatePaymentSessionAsync(int invoiceId, decimal amount);
        Task<bool> ValidatePaymentSessionAsync(string sessionId);
        Task<bool> ExpirePaymentSessionAsync(string sessionId);
        Task CleanupExpiredSessionsAsync();

        // التنبيهات والإشعارات
        Task SendPaymentConfirmationAsync(int transactionId);
        Task SendPaymentFailureNotificationAsync(int transactionId);
        Task SendRefundNotificationAsync(int transactionId);
        Task<bool> EnablePaymentNotificationsAsync(bool enable);

        // الصيانة والتحسين
        Task<object> GetSystemPerformanceMetricsAsync();
        Task<bool> OptimizePaymentProcessingAsync();
        Task CleanupOldTransactionsAsync(int daysToKeep = 365);
        Task<bool> ArchiveOldDataAsync(DateTime cutoffDate);
        Task<object> GetSystemHealthAsync();

        // التكامل مع البوابات المختلفة
        Task<object> GetGatewaySpecificSettingsAsync(PaymentGatewayType gatewayType);
        Task<bool> UpdateGatewaySpecificSettingsAsync(PaymentGatewayType gatewayType, object settings);
        Task<bool> TestGatewayIntegrationAsync(PaymentGatewayType gatewayType);
        Task<object> GetGatewayCapabilitiesAsync(PaymentGatewayType gatewayType);

        // إدارة الاشتراكات والدفعات المتكررة
        Task<int> CreateRecurringPaymentAsync(int studentId, decimal amount, string frequency);
        Task<bool> ProcessRecurringPaymentsAsync();
        Task<IEnumerable<object>> GetActiveSubscriptionsAsync();
        Task<bool> CancelSubscriptionAsync(int subscriptionId);

        // التحليلات المتقدمة
        Task<object> GetPaymentTrendsAsync(int months = 12);
        Task<object> GetCustomerPaymentBehaviorAsync();
        Task<object> GetGatewayComparisonAnalysisAsync();
        Task<object> PredictPaymentVolumeAsync(int monthsAhead = 3);
    }
}
