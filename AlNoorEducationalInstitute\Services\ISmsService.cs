using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models.Integration;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة الرسائل القصيرة
    /// SMS service interface
    /// </summary>
    public interface ISmsService
    {
        // إعدادات بوابة الرسائل
        Task<SmsGatewaySettings?> GetActiveGatewaySettingsAsync();
        Task<IEnumerable<SmsGatewaySettings>> GetAllGatewaySettingsAsync();
        Task<int> SaveGatewaySettingsAsync(SmsGatewaySettings settings);
        Task<bool> UpdateGatewaySettingsAsync(SmsGatewaySettings settings);
        Task<bool> DeleteGatewaySettingsAsync(int settingsId);
        Task<bool> ActivateGatewayAsync(int settingsId);
        Task<bool> TestGatewayConnectionAsync(int settingsId);

        // إرسال الرسائل الفردية
        Task<int> SendSmsAsync(string phoneNumber, string message, SmsMessageType messageType = SmsMessageType.General);
        Task<int> SendSmsAsync(SmsMessage smsMessage);
        Task<bool> SendSmsToStudentAsync(int studentId, string message, SmsMessageType messageType = SmsMessageType.General);
        Task<bool> SendSmsToParentAsync(int studentId, string message, SmsMessageType messageType = SmsMessageType.General);
        Task<bool> SendSmsToEmployeeAsync(int employeeId, string message, SmsMessageType messageType = SmsMessageType.General);

        // إرسال الرسائل الجماعية
        Task<int> CreateBulkSmsAsync(BulkSmsMessage bulkMessage);
        Task<bool> SendBulkSmsAsync(int bulkMessageId);
        Task<bool> SendBulkSmsToAllStudentsAsync(string message, SmsMessageType messageType = SmsMessageType.General);
        Task<bool> SendBulkSmsToAllParentsAsync(string message, SmsMessageType messageType = SmsMessageType.General);
        Task<bool> SendBulkSmsToClassAsync(int classId, string message, SmsMessageType messageType = SmsMessageType.General);
        Task<bool> SendBulkSmsToGradeAsync(int grade, string message, SmsMessageType messageType = SmsMessageType.General);
        Task<bool> SendBulkSmsToLevelAsync(EducationLevel level, string message, SmsMessageType messageType = SmsMessageType.General);

        // جدولة الرسائل
        Task<int> ScheduleSmsAsync(string phoneNumber, string message, DateTime scheduledDate, SmsMessageType messageType = SmsMessageType.General);
        Task<int> ScheduleBulkSmsAsync(BulkSmsMessage bulkMessage, DateTime scheduledDate);
        Task<IEnumerable<SmsMessage>> GetScheduledMessagesAsync();
        Task<IEnumerable<BulkSmsMessage>> GetScheduledBulkMessagesAsync();
        Task ProcessScheduledMessagesAsync();

        // إدارة الرسائل
        Task<IEnumerable<SmsMessage>> GetAllMessagesAsync();
        Task<IEnumerable<SmsMessage>> GetMessagesByStatusAsync(SmsMessageStatus status);
        Task<IEnumerable<SmsMessage>> GetMessagesByTypeAsync(SmsMessageType messageType);
        Task<IEnumerable<SmsMessage>> GetMessagesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<SmsMessage?> GetMessageByIdAsync(int messageId);
        Task<bool> UpdateMessageStatusAsync(int messageId, SmsMessageStatus status, string statusMessage = "");
        Task<bool> RetryFailedMessageAsync(int messageId);
        Task<bool> CancelScheduledMessageAsync(int messageId);

        // إدارة الرسائل الجماعية
        Task<IEnumerable<BulkSmsMessage>> GetAllBulkMessagesAsync();
        Task<BulkSmsMessage?> GetBulkMessageByIdAsync(int bulkMessageId);
        Task<bool> UpdateBulkMessageStatusAsync(int bulkMessageId, BulkMessageStatus status);
        Task<bool> CancelBulkMessageAsync(int bulkMessageId);
        Task<IEnumerable<SmsMessage>> GetBulkMessageDetailsAsync(int bulkMessageId);

        // قوالب الرسائل
        Task<IEnumerable<MessageTemplate>> GetAllTemplatesAsync();
        Task<IEnumerable<MessageTemplate>> GetTemplatesByTypeAsync(SmsMessageType messageType);
        Task<MessageTemplate?> GetTemplateByIdAsync(int templateId);
        Task<int> CreateTemplateAsync(MessageTemplate template);
        Task<bool> UpdateTemplateAsync(MessageTemplate template);
        Task<bool> DeleteTemplateAsync(int templateId);
        Task<string> ProcessTemplateAsync(int templateId, Dictionary<string, object> variables);

        // الرسائل التلقائية
        Task SendAttendanceNotificationAsync(int studentId, DateTime date, bool isAbsent);
        Task SendGradeNotificationAsync(int studentId, string subjectName, decimal grade, decimal maxGrade);
        Task SendPaymentReminderAsync(int studentId, decimal amount, DateTime dueDate);
        Task SendPaymentConfirmationAsync(int studentId, decimal amount, string receiptNumber);
        Task SendWelcomeMessageAsync(int studentId);
        Task SendEmergencyNotificationAsync(string message);

        // التقارير والإحصائيات
        Task<object> GetSmsStatisticsAsync(DateTime startDate, DateTime endDate);
        Task<object> GetSmsUsageReportAsync(DateTime startDate, DateTime endDate);
        Task<object> GetSmsDeliveryReportAsync(DateTime startDate, DateTime endDate);
        Task<decimal> GetSmsCostReportAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<object>> GetTopSmsRecipientsAsync(int count = 10);

        // إدارة الأرقام
        Task<bool> ValidatePhoneNumberAsync(string phoneNumber);
        Task<string> FormatPhoneNumberAsync(string phoneNumber, string countryCode = "+966");
        Task<IEnumerable<string>> GetBlacklistedNumbersAsync();
        Task<bool> AddToBlacklistAsync(string phoneNumber);
        Task<bool> RemoveFromBlacklistAsync(string phoneNumber);
        Task<bool> IsNumberBlacklistedAsync(string phoneNumber);

        // إدارة الرصيد والتكلفة
        Task<decimal> GetAccountBalanceAsync();
        Task<decimal> CalculateMessageCostAsync(string message, string phoneNumber);
        Task<decimal> CalculateBulkMessageCostAsync(BulkSmsMessage bulkMessage);
        Task<bool> HasSufficientBalanceAsync(decimal requiredAmount);
        Task<object> GetUsageStatisticsAsync(DateTime startDate, DateTime endDate);

        // التكامل مع النظام
        Task<bool> EnableAutoNotificationsAsync(SmsMessageType messageType, bool enable);
        Task<Dictionary<SmsMessageType, bool>> GetAutoNotificationSettingsAsync();
        Task<bool> UpdateAutoNotificationSettingsAsync(Dictionary<SmsMessageType, bool> settings);

        // سجلات التكامل
        Task<IEnumerable<IntegrationLog>> GetSmsLogsAsync(DateTime startDate, DateTime endDate);
        Task LogSmsOperationAsync(string operation, string requestData, string responseData, bool isSuccess, string errorMessage = "");
        Task<object> GetSmsPerformanceMetricsAsync();

        // الصيانة والتنظيف
        Task CleanupOldMessagesAsync(int daysToKeep = 90);
        Task CleanupOldLogsAsync(int daysToKeep = 30);
        Task<bool> ArchiveOldDataAsync(DateTime cutoffDate);
        Task<object> GetSystemHealthAsync();
    }
}
