using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AlNoorEducationalInstitute.Models;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// واجهة خدمة إدارة المواد الدراسية
    /// Subject management service interface
    /// </summary>
    public interface ISubjectService
    {
        /// <summary>
        /// الحصول على جميع المواد
        /// </summary>
        Task<IEnumerable<Subject>> GetAllSubjectsAsync();

        /// <summary>
        /// الحصول على مادة بواسطة المعرف
        /// </summary>
        Task<Subject?> GetSubjectByIdAsync(int subjectId);

        /// <summary>
        /// الحصول على مادة بواسطة الكود
        /// </summary>
        Task<Subject?> GetSubjectByCodeAsync(string subjectCode);

        /// <summary>
        /// الحصول على المواد بواسطة المستوى التعليمي
        /// </summary>
        Task<IEnumerable<Subject>> GetSubjectsByLevelAsync(EducationLevel level);

        /// <summary>
        /// الحصول على المواد بواسطة الصف الدراسي
        /// </summary>
        Task<IEnumerable<Subject>> GetSubjectsByGradeAsync(int grade);

        /// <summary>
        /// الحصول على المواد بواسطة النوع
        /// </summary>
        Task<IEnumerable<Subject>> GetSubjectsByTypeAsync(SubjectType type);

        /// <summary>
        /// الحصول على المواد بواسطة المدرس
        /// </summary>
        Task<IEnumerable<Subject>> GetSubjectsByTeacherAsync(int teacherId);

        /// <summary>
        /// البحث عن المواد بواسطة الاسم
        /// </summary>
        Task<IEnumerable<Subject>> SearchSubjectsByNameAsync(string name);

        /// <summary>
        /// إضافة مادة جديدة
        /// </summary>
        Task<int> AddSubjectAsync(Subject subject);

        /// <summary>
        /// تحديث بيانات مادة
        /// </summary>
        Task<bool> UpdateSubjectAsync(Subject subject);

        /// <summary>
        /// حذف مادة
        /// </summary>
        Task<bool> DeleteSubjectAsync(int subjectId);

        /// <summary>
        /// تغيير حالة المادة
        /// </summary>
        Task<bool> ChangeSubjectStatusAsync(int subjectId, SubjectStatus newStatus, int userId);

        /// <summary>
        /// تعيين مدرس للمادة
        /// </summary>
        Task<bool> AssignTeacherToSubjectAsync(int subjectId, int teacherId, int userId);

        /// <summary>
        /// إزالة مدرس من المادة
        /// </summary>
        Task<bool> RemoveTeacherFromSubjectAsync(int subjectId, int userId);

        /// <summary>
        /// التحقق من وجود كود المادة
        /// </summary>
        Task<bool> IsSubjectCodeExistsAsync(string subjectCode, int? excludeSubjectId = null);

        /// <summary>
        /// الحصول على إحصائيات المواد
        /// </summary>
        Task<SubjectStatistics> GetSubjectStatisticsAsync();

        /// <summary>
        /// ربط مادة بفصل دراسي
        /// </summary>
        Task<bool> AssignSubjectToClassAsync(int subjectId, int classId, int? teacherId, string academicYear, int userId);

        /// <summary>
        /// إلغاء ربط مادة من فصل دراسي
        /// </summary>
        Task<bool> UnassignSubjectFromClassAsync(int subjectId, int classId, string academicYear, int userId);

        /// <summary>
        /// الحصول على المواد المرتبطة بفصل معين
        /// </summary>
        Task<IEnumerable<Subject>> GetSubjectsByClassAsync(int classId, string academicYear);
    }

    /// <summary>
    /// إحصائيات المواد
    /// </summary>
    public class SubjectStatistics
    {
        public int TotalSubjects { get; set; }
        public int ActiveSubjects { get; set; }
        public int SuspendedSubjects { get; set; }
        public int CancelledSubjects { get; set; }
        public Dictionary<SubjectType, int> SubjectsByType { get; set; } = new();
        public Dictionary<EducationLevel, int> SubjectsByLevel { get; set; } = new();
        public Dictionary<int, int> SubjectsByGrade { get; set; } = new();
        public int SubjectsWithTeacher { get; set; }
        public int SubjectsWithoutTeacher { get; set; }
        public double AverageWeeklyHours { get; set; }
    }
}
