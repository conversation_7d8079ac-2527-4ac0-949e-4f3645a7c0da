# دليل المطور - Developer Guide

## نظام إدارة مؤسسة النور التربوي
### Al-Noor Educational Institute Management System

هذا الدليل مخصص للمطورين الذين يرغبون في المساهمة في تطوير النظام أو فهم بنيته التقنية.

---

## 🏗️ معمارية النظام - System Architecture

### نمط المعمارية
النظام يتبع نمط **Layered Architecture** مع **Dependency Injection**:

```
┌─────────────────────────────────────┐
│           Presentation Layer        │
│              (Forms)                │
├─────────────────────────────────────┤
│            Business Layer           │
│             (Services)              │
├─────────────────────────────────────┤
│           Data Access Layer         │
│         (DatabaseManager)           │
├─────────────────────────────────────┤
│             Data Layer              │
│            (SQLite DB)              │
└─────────────────────────────────────┘
```

### المكونات الرئيسية

#### 1. Models (النماذج)
```csharp
// مثال: نموذج الطالب
public class Student
{
    public int StudentId { get; set; }
    public string StudentNumber { get; set; }
    public string FullNameArabic { get; set; }
    // ... باقي الخصائص
}
```

#### 2. Services (الخدمات)
```csharp
// واجهة الخدمة
public interface IStudentService
{
    Task<IEnumerable<Student>> GetAllStudentsAsync();
    Task<Student?> GetStudentByIdAsync(int studentId);
    // ... باقي الدوال
}

// تطبيق الخدمة
public class StudentService : IStudentService
{
    private readonly DatabaseManager _databaseManager;
    // ... التطبيق
}
```

#### 3. Data Access (الوصول للبيانات)
```csharp
public class DatabaseManager
{
    public void InitializeDatabase() { }
    public SQLiteConnection GetConnection() { }
    // ... دوال إدارة قاعدة البيانات
}
```

#### 4. Forms (النوافذ)
```csharp
public partial class StudentManagementForm : Form
{
    private readonly IStudentService _studentService;
    
    public StudentManagementForm(IStudentService studentService)
    {
        _studentService = studentService;
        InitializeComponent();
    }
}
```

---

## 🔧 إعداد بيئة التطوير - Development Environment Setup

### المتطلبات
- **Visual Studio 2022** أو **Visual Studio Code**
- **.NET 6.0 SDK**
- **Git**
- **SQLite Browser** (اختياري للتطوير)

### خطوات الإعداد

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-repo/AlNoorEducationalInstitute.git
cd AlNoorEducationalInstitute
```

2. **استعادة الحزم**
```bash
dotnet restore
```

3. **بناء المشروع**
```bash
dotnet build
```

4. **تشغيل في وضع التطوير**
```bash
cd AlNoorEducationalInstitute
dotnet run --environment Development
```

---

## 📊 قاعدة البيانات - Database

### تصميم قاعدة البيانات

#### جدول المستخدمين (Users)
```sql
CREATE TABLE Users (
    UserId INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL UNIQUE,
    PasswordHash TEXT NOT NULL,
    FullName TEXT NOT NULL,
    Email TEXT NOT NULL UNIQUE,
    Role INTEGER NOT NULL,
    Status INTEGER NOT NULL DEFAULT 1,
    -- ... باقي الأعمدة
);
```

#### جدول الطلاب (Students)
```sql
CREATE TABLE Students (
    StudentId INTEGER PRIMARY KEY AUTOINCREMENT,
    StudentNumber TEXT NOT NULL UNIQUE,
    FullNameArabic TEXT NOT NULL,
    DateOfBirth DATE NOT NULL,
    Gender INTEGER NOT NULL,
    CurrentClassId INTEGER,
    -- ... باقي الأعمدة
    FOREIGN KEY (CurrentClassId) REFERENCES Classes(ClassId)
);
```

### إدارة قاعدة البيانات

#### إنشاء Migration جديد
```csharp
// في DatabaseManager.cs
private void CreateNewTable(SQLiteConnection connection)
{
    var sql = @"
        CREATE TABLE IF NOT EXISTS NewTable (
            Id INTEGER PRIMARY KEY AUTOINCREMENT,
            Name TEXT NOT NULL,
            CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        );";
    
    using var command = new SQLiteCommand(sql, connection);
    command.ExecuteNonQuery();
}
```

#### إضافة بيانات أولية
```csharp
private void InsertSeedData(SQLiteConnection connection)
{
    var sql = "INSERT INTO Table (Column1, Column2) VALUES (@Value1, @Value2)";
    using var command = new SQLiteCommand(sql, connection);
    command.Parameters.AddWithValue("@Value1", "value1");
    command.Parameters.AddWithValue("@Value2", "value2");
    command.ExecuteNonQuery();
}
```

---

## 🔐 الأمان - Security

### تشفير كلمات المرور
```csharp
// تشفير كلمة المرور
public string HashPassword(string password)
{
    return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
}

// التحقق من كلمة المرور
public bool VerifyPassword(string password, string hashedPassword)
{
    return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
}
```

### إدارة الجلسات
```csharp
public static class CurrentUser
{
    public static User? User { get; set; }
    public static bool IsLoggedIn => User != null;
    public static bool HasRole(UserRole role) => User?.Role == role;
}
```

### سجل الأمان
```csharp
public async Task LogSecurityEventAsync(SecurityEventType eventType, int? userId, string? details)
{
    // تسجيل الحدث في قاعدة البيانات
    var sql = @"
        INSERT INTO AuditLog (EventType, UserId, Details, Timestamp)
        VALUES (@EventType, @UserId, @Details, @Timestamp)";
    
    // ... تنفيذ الاستعلام
}
```

---

## 🎨 تطوير واجهة المستخدم - UI Development

### إرشادات التصميم

#### الألوان المستخدمة
```csharp
public static class AppColors
{
    public static readonly Color Primary = Color.FromArgb(70, 130, 180);
    public static readonly Color Success = Color.FromArgb(34, 139, 34);
    public static readonly Color Warning = Color.FromArgb(255, 165, 0);
    public static readonly Color Danger = Color.FromArgb(220, 20, 60);
    public static readonly Color Background = Color.FromArgb(245, 245, 245);
}
```

#### الخطوط
```csharp
public static class AppFonts
{
    public static readonly Font Regular = new Font("Tahoma", 10F, FontStyle.Regular);
    public static readonly Font Bold = new Font("Tahoma", 10F, FontStyle.Bold);
    public static readonly Font Title = new Font("Tahoma", 14F, FontStyle.Bold);
}
```

#### إنشاء نافذة جديدة
```csharp
public partial class NewForm : Form
{
    private readonly IService _service;

    public NewForm(IService service)
    {
        _service = service;
        InitializeComponent();
        SetupForm();
    }

    private void SetupForm()
    {
        // إعداد النافذة
        this.RightToLeft = RightToLeft.Yes;
        this.RightToLeftLayout = true;
        this.Font = AppFonts.Regular;
        
        // إعداد الأحداث
        this.Load += NewForm_Load;
    }
}
```

---

## 🧪 الاختبار - Testing

### اختبار الوحدة (Unit Testing)
```csharp
[TestClass]
public class StudentServiceTests
{
    private IStudentService _studentService;
    private Mock<DatabaseManager> _mockDatabaseManager;

    [TestInitialize]
    public void Setup()
    {
        _mockDatabaseManager = new Mock<DatabaseManager>();
        _studentService = new StudentService(_mockDatabaseManager.Object, Mock.Of<ILogger<StudentService>>());
    }

    [TestMethod]
    public async Task GetAllStudentsAsync_ShouldReturnStudents()
    {
        // Arrange
        var expectedStudents = new List<Student> { /* ... */ };
        
        // Act
        var result = await _studentService.GetAllStudentsAsync();
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(expectedStudents.Count, result.Count());
    }
}
```

### اختبار التكامل (Integration Testing)
```csharp
[TestClass]
public class DatabaseIntegrationTests
{
    private DatabaseManager _databaseManager;

    [TestInitialize]
    public void Setup()
    {
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.Test.json")
            .Build();
        
        _databaseManager = new DatabaseManager(configuration, Mock.Of<ILogger<DatabaseManager>>());
        _databaseManager.InitializeDatabase();
    }

    [TestMethod]
    public void InitializeDatabase_ShouldCreateTables()
    {
        // Test database initialization
        using var connection = _databaseManager.GetConnection();
        connection.Open();
        
        var sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='Users'";
        using var command = new SQLiteCommand(sql, connection);
        var result = command.ExecuteScalar();
        
        Assert.IsNotNull(result);
    }
}
```

---

## 📝 معايير الكود - Coding Standards

### تسمية المتغيرات والدوال
```csharp
// ✅ صحيح
public class StudentService
{
    private readonly ILogger<StudentService> _logger;
    
    public async Task<Student?> GetStudentByIdAsync(int studentId)
    {
        var student = await FindStudentInDatabaseAsync(studentId);
        return student;
    }
}

// ❌ خطأ
public class studentservice
{
    private ILogger logger;
    
    public Student GetStudent(int id)
    {
        var s = FindStudent(id);
        return s;
    }
}
```

### التعليقات والتوثيق
```csharp
/// <summary>
/// يحصل على طالب بواسطة المعرف الفريد
/// Gets a student by unique identifier
/// </summary>
/// <param name="studentId">معرف الطالب الفريد</param>
/// <returns>بيانات الطالب أو null إذا لم يوجد</returns>
public async Task<Student?> GetStudentByIdAsync(int studentId)
{
    // التحقق من صحة المعرف
    if (studentId <= 0)
    {
        _logger.LogWarning("معرف طالب غير صحيح: {StudentId}", studentId);
        return null;
    }

    try
    {
        // البحث في قاعدة البيانات
        using var connection = _databaseManager.GetConnection();
        // ... باقي الكود
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "خطأ في الحصول على الطالب {StudentId}", studentId);
        throw;
    }
}
```

### معالجة الأخطاء
```csharp
public async Task<bool> UpdateStudentAsync(Student student)
{
    try
    {
        // التحقق من صحة البيانات
        if (student == null)
            throw new ArgumentNullException(nameof(student));

        if (string.IsNullOrWhiteSpace(student.FullNameArabic))
            throw new ArgumentException("اسم الطالب مطلوب", nameof(student));

        // تنفيذ العملية
        using var connection = _databaseManager.GetConnection();
        await connection.OpenAsync();
        
        // ... تنفيذ التحديث
        
        _logger.LogInformation("تم تحديث بيانات الطالب {StudentId}", student.StudentId);
        return true;
    }
    catch (ArgumentException ex)
    {
        _logger.LogWarning(ex, "بيانات طالب غير صحيحة");
        throw;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "خطأ في تحديث بيانات الطالب {StudentId}", student.StudentId);
        return false;
    }
}
```

---

## 🚀 النشر - Deployment

### بناء للإنتاج
```bash
# بناء Release
dotnet build --configuration Release

# نشر مستقل
dotnet publish --configuration Release --self-contained true --runtime win-x64
```

### إنشاء Installer
```bash
# استخدام WiX Toolset أو Inno Setup
# أو أدوات أخرى لإنشاء ملف تثبيت
```

---

## 🔄 المساهمة - Contributing

### خطوات المساهمة

1. **Fork المشروع**
2. **إنشاء فرع للميزة الجديدة**
```bash
git checkout -b feature/new-feature
```

3. **كتابة الكود مع الاختبارات**
4. **Commit التغييرات**
```bash
git commit -m "Add new feature: description"
```

5. **Push للفرع**
```bash
git push origin feature/new-feature
```

6. **إنشاء Pull Request**

### معايير المراجعة
- ✅ الكود يتبع معايير المشروع
- ✅ جميع الاختبارات تمر بنجاح
- ✅ التوثيق محدث
- ✅ لا توجد تحذيرات في البناء
- ✅ الأمان مراعى في التغييرات

---

## 📞 الدعم التقني - Technical Support

للحصول على المساعدة التقنية:
- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [رابط المشروع]
- **التوثيق**: [رابط التوثيق]

---

**© 2025 مؤسسة النور التربوي - فريق التطوير**
