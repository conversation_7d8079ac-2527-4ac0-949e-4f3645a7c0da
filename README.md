# نظام إدارة مؤسسة النور التربوي
## Al-Noor Educational Institute Management System

نظام إدارة شامل ومتكامل لمؤسسة النور التربوي، مصمم لتسهيل إدارة العمليات التعليمية والإدارية بكفاءة عالية.

## 🌟 المميزات الرئيسية

### إدارة الطلاب
- ✅ إضافة وتعديل بيانات الطلاب الشخصية والأكاديمية
- ✅ نظام بحث متقدم بالاسم والرقم التعريفي
- ✅ إدارة حالات الطلاب (نشط، متخرج، منقطع، منقول)
- ✅ ربط الطلاب بالفصول الدراسية
- ✅ حفظ بيانات أولياء الأمور ومعلومات الاتصال

### إدارة الموظفين
- ✅ إدارة شاملة لبيانات الموظفين
- ✅ تصنيف الموظفين حسب المناصب والأقسام
- ✅ إدارة المؤهلات العلمية والخبرات
- ✅ نظام الرواتب والحالات الوظيفية
- ✅ ربط الموظفين بحسابات المستخدمين

### إدارة الفصول الدراسية
- ✅ إنشاء وإدارة الفصول حسب المستويات التعليمية
- ✅ تعيين المدرسين المسؤولين عن الفصول
- ✅ إدارة أعداد الطلاب والحد الأقصى لكل فصل
- ✅ تنظيم الفصول حسب الأعوام الدراسية
- ✅ إدارة القاعات الدراسية

### إدارة المواد الدراسية
- ✅ إضافة وتصنيف المواد الدراسية
- ✅ ربط المواد بالمدرسين والفصول
- ✅ إدارة المناهج والكتب المدرسية
- ✅ تحديد الدرجات والساعات الأسبوعية
- ✅ إدارة متطلبات المواد

### نظام المصادقة والأمان
- ✅ نظام تسجيل دخول آمن مع تشفير كلمات المرور
- ✅ إدارة الأدوار والصلاحيات المتقدمة
- ✅ حماية من محاولات الدخول غير المصرح بها
- ✅ قفل الحسابات تلقائياً عند تجاوز المحاولات المسموحة
- ✅ سجل أمان شامل لجميع العمليات

## 🛠️ التقنيات المستخدمة

- **لغة البرمجة**: C# (.NET 6)
- **واجهة المستخدم**: Windows Forms
- **قاعدة البيانات**: SQLite
- **الأمان**: BCrypt لتشفير كلمات المرور
- **إدارة التبعيات**: Microsoft Extensions (DI, Configuration, Logging)
- **بيئة التطوير**: Visual Studio 2022

## 📋 متطلبات النظام

### الحد الأدنى
- Windows 10 أو أحدث
- .NET 6.0 Runtime
- 4 GB RAM
- 500 MB مساحة تخزين فارغة
- دقة شاشة 1024x768 أو أعلى

### الموصى به
- Windows 11
- 8 GB RAM أو أكثر
- 2 GB مساحة تخزين فارغة
- دقة شاشة 1920x1080 أو أعلى

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/AlNoorEducationalInstitute.git
cd AlNoorEducationalInstitute
```

### 2. بناء المشروع
```bash
dotnet restore
dotnet build --configuration Release
```

### 3. تشغيل التطبيق
```bash
dotnet run --project AlNoorEducationalInstitute
```

أو تشغيل الملف التنفيذي مباشرة:
```bash
cd bin/Release/net6.0-windows
./AlNoorEducationalInstitute.exe
```

## 👤 بيانات الدخول الافتراضية

عند التشغيل الأول، يمكنك استخدام الحساب التالي:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

> ⚠️ **تنبيه أمني**: يُنصح بشدة بتغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## 📁 هيكل المشروع

```
AlNoorEducationalInstitute/
├── Models/                 # نماذج البيانات
│   ├── Student.cs         # نموذج الطالب
│   ├── Employee.cs        # نموذج الموظف
│   ├── Class.cs           # نموذج الفصل
│   ├── Subject.cs         # نموذج المادة
│   └── User.cs            # نموذج المستخدم
├── Data/                  # طبقة البيانات
│   └── DatabaseManager.cs # مدير قاعدة البيانات
├── Services/              # طبقة الخدمات
│   ├── Interfaces/        # واجهات الخدمات
│   └── Implementations/   # تطبيقات الخدمات
├── Forms/                 # نوافذ التطبيق
│   ├── LoginForm.cs       # نافذة تسجيل الدخول
│   ├── MainForm.cs        # النافذة الرئيسية
│   └── Management/        # نوافذ الإدارة
├── Database/              # ملفات قاعدة البيانات
├── Logs/                  # ملفات السجلات
└── appsettings.json       # ملف الإعدادات
```

## 🔧 الإعدادات

يمكن تخصيص النظام من خلال ملف `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=Database\\AlNoorDB.db;Version=3;"
  },
  "ApplicationSettings": {
    "InstituteName": "مؤسسة النور التربوي",
    "MaxLoginAttempts": 3,
    "SessionTimeoutMinutes": 30
  },
  "Security": {
    "PasswordMinLength": 8,
    "RequireSpecialCharacters": true,
    "AccountLockoutDuration": 15
  }
}
```

## 🔐 الأدوار والصلاحيات

### أدوار المستخدمين
1. **مدير النظام الرئيسي** - صلاحيات كاملة
2. **مدير النظام** - إدارة المستخدمين والإعدادات
3. **مدير المؤسسة** - إدارة العمليات التعليمية
4. **نائب المدير** - صلاحيات محدودة للإدارة
5. **مدرس** - إدارة الطلاب والدرجات
6. **موظف إداري** - العمليات الإدارية
7. **موظف استقبال** - إدارة الطلاب فقط

## 📊 قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite مع الجداول التالية:
- `Users` - المستخدمين
- `Employees` - الموظفين
- `Students` - الطلاب
- `Classes` - الفصول الدراسية
- `Subjects` - المواد الدراسية
- `ClassSubjects` - ربط الفصول بالمواد
- `AuditLog` - سجل العمليات

## 🔄 النسخ الاحتياطي

- النظام ينشئ نسخ احتياطية تلقائية كل 24 ساعة
- يمكن إنشاء نسخة احتياطية يدوية من قائمة الأدوات
- النسخ الاحتياطية تُحفظ في مجلد `Database/Backup/`

## 📝 السجلات

- جميع العمليات تُسجل في ملفات السجلات
- السجلات تُحفظ في مجلد `Logs/`
- يتم إنشاء ملف سجل جديد يومياً
- السجلات القديمة تُحذف تلقائياً بعد 30 يوم

## 🤝 المساهمة

نرحب بمساهماتكم لتطوير النظام:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **الموقع الإلكتروني**: www.alnoor.edu

## 🙏 شكر وتقدير

نشكر جميع المساهمين في تطوير هذا النظام وجعله أداة فعالة لإدارة المؤسسات التعليمية.

---

**© 2025 مؤسسة النور التربوي. جميع الحقوق محفوظة.**
