# نظام إدارة مؤسسة النور التربوي
# Al-Noor Educational Institute Management System

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    نظام إدارة مؤسسة النور التربوي" -ForegroundColor Green
Write-Host "    Al-Noor Educational Institute" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "جاري التحقق من متطلبات النظام..." -ForegroundColor Yellow
Write-Host "Checking system requirements..." -ForegroundColor Yellow

# Check if .NET 6 is installed
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET 6.0 غير مثبت على النظام" -ForegroundColor Red
    Write-Host "❌ .NET 6.0 is not installed" -ForegroundColor Red
    Write-Host "يرجى تحميل وتثبيت .NET 6.0 من: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

Write-Host ""
Write-Host "جاري استعادة الحزم..." -ForegroundColor Yellow
Write-Host "Restoring packages..." -ForegroundColor Yellow

dotnet restore

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في استعادة الحزم" -ForegroundColor Red
    Write-Host "❌ Package restore failed" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

Write-Host ""
Write-Host "جاري بناء المشروع..." -ForegroundColor Yellow
Write-Host "Building project..." -ForegroundColor Yellow

dotnet build --configuration Release

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء المشروع" -ForegroundColor Red
    Write-Host "❌ Build failed" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

Write-Host ""
Write-Host "✅ تم بناء المشروع بنجاح" -ForegroundColor Green
Write-Host "✅ Build successful" -ForegroundColor Green
Write-Host ""

Write-Host "جاري تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host "Starting application..." -ForegroundColor Yellow
Write-Host ""

# Change to project directory and run
Set-Location -Path "AlNoorEducationalInstitute"
dotnet run

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ فشل في تشغيل التطبيق" -ForegroundColor Red
    Write-Host "❌ Failed to start application" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

Write-Host ""
Write-Host "تم إغلاق التطبيق" -ForegroundColor Green
Write-Host "Application closed" -ForegroundColor Green
Read-Host "اضغط Enter للخروج..."
