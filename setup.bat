@echo off
chcp 65001 > nul
echo ========================================
echo    إعداد نظام إدارة مؤسسة النور التربوي
echo    Al-Noor Educational Institute Setup
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...
echo Checking system requirements...
echo.

REM Check if .NET 6 is installed
dotnet --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET 6.0 غير مثبت على النظام
    echo ❌ .NET 6.0 is not installed
    echo.
    echo يرجى تحميل وتثبيت .NET 6.0 من:
    echo Please download and install .NET 6.0 from:
    echo https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

echo ✅ .NET 6.0 مثبت بنجاح
echo ✅ .NET 6.0 is installed
echo.

echo جاري إنشاء المجلدات المطلوبة...
echo Creating required directories...

if not exist "AlNoorEducationalInstitute\Database" mkdir "AlNoorEducationalInstitute\Database"
if not exist "AlNoorEducationalInstitute\Database\Backup" mkdir "AlNoorEducationalInstitute\Database\Backup"
if not exist "AlNoorEducationalInstitute\Logs" mkdir "AlNoorEducationalInstitute\Logs"

echo ✅ تم إنشاء المجلدات بنجاح
echo ✅ Directories created successfully
echo.

echo جاري استعادة حزم NuGet...
echo Restoring NuGet packages...

dotnet restore

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في استعادة الحزم
    echo ❌ Package restore failed
    pause
    exit /b 1
)

echo ✅ تم استعادة الحزم بنجاح
echo ✅ Packages restored successfully
echo.

echo جاري بناء المشروع...
echo Building project...

dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo ✅ Project built successfully
echo.

echo ========================================
echo           تم الإعداد بنجاح!
echo         Setup completed successfully!
echo ========================================
echo.
echo يمكنك الآن تشغيل التطبيق باستخدام:
echo You can now run the application using:
echo.
echo   run.bat
echo   أو / or
echo   run.ps1
echo.
echo بيانات الدخول الافتراضية:
echo Default login credentials:
echo   اسم المستخدم / Username: admin
echo   كلمة المرور / Password: admin123
echo.
echo ⚠️  يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول
echo ⚠️  It's recommended to change the password after first login
echo.
pause
